<?php
/**
 * Content processor for AI Blog Post Generator
 * Handles markdown to HTML conversion and content formatting
 */
class AIBPG_Content_Processor {
    
    /**
     * Process raw AI-generated content into clean HTML
     */
    public static function process_content( $raw_content ) {
        if ( empty( $raw_content ) ) {
            return '';
        }
        
        // Step 1: Clean up the raw content
        $content = self::clean_raw_content( $raw_content );
        
        // Step 2: Convert markdown to HTML
        $content = self::markdown_to_html( $content );
        
        // Step 3: Enhance formatting
        $content = self::enhance_formatting( $content );
        
        // Step 4: Process internal links
        $content = self::process_internal_links( $content );
        
        // Step 5: Final cleanup and validation
        $content = self::final_cleanup( $content );
        
        return $content;
    }
    
    /**
     * Clean up raw AI-generated content
     */
    private static function clean_raw_content( $content ) {
        // Remove any leading/trailing whitespace
        $content = trim( $content );
        
        // Normalize line endings
        $content = str_replace( ["\r\n", "\r"], "\n", $content );
        
        // Remove excessive blank lines (more than 2 consecutive)
        $content = preg_replace( '/\n{3,}/', "\n\n", $content );
        
        // Fix common AI formatting issues
        $content = str_replace( ['**bold**', '*italic*'], ['<strong>bold</strong>', '<em>italic</em>'], $content );
        
        return $content;
    }
    
    /**
     * Convert markdown syntax to HTML
     */
    private static function markdown_to_html( $content ) {
        // Convert headings (H1-H6) with bold formatting
        $content = preg_replace( '/^#{6}\s*(.+)$/m', '<h6><strong>$1</strong></h6>', $content );
        $content = preg_replace( '/^#{5}\s*(.+)$/m', '<h5><strong>$1</strong></h5>', $content );
        $content = preg_replace( '/^#{4}\s*(.+)$/m', '<h4><strong>$1</strong></h4>', $content );
        $content = preg_replace( '/^#{3}\s*(.+)$/m', '<h3><strong>$1</strong></h3>', $content );
        $content = preg_replace( '/^#{2}\s*(.+)$/m', '<h2><strong>$1</strong></h2>', $content );
        $content = preg_replace( '/^#{1}\s*(.+)$/m', '<h1><strong>$1</strong></h1>', $content );
        
        // Handle headings with markdown bold syntax (### **Title**)
        $content = preg_replace( '/^(#{1,6})\s*\*\*(.+?)\*\*\s*$/m', '<h$1><strong>$2</strong></h$1>', $content );
        
        // Convert bold text (**text** or __text__)
        $content = preg_replace( '/\*\*(.+?)\*\*/', '<strong>$1</strong>', $content );
        $content = preg_replace( '/__(.+?)__/', '<strong>$1</strong>', $content );
        
        // Convert italic text (*text* or _text_)
        $content = preg_replace( '/\*([^*]+)\*/', '<em>$1</em>', $content );
        $content = preg_replace( '/_([^_]+)_/', '<em>$1</em>', $content );
        
        // Convert unordered lists
        $content = preg_replace( '/^[\*\-\+]\s+(.+)$/m', '<li>$1</li>', $content );
        
        // Convert numbered lists
        $content = preg_replace( '/^\d+\.\s+(.+)$/m', '<li>$1</li>', $content );
        
        // Wrap consecutive list items in ul/ol tags
        $content = self::wrap_lists( $content );
        
        // Convert links [text](url)
        $content = preg_replace( '/\[([^\]]+)\]\(([^)]+)\)/', '<a href="$2" target="_blank">$1</a>', $content );
        
        // Convert code blocks ```code```
        $content = preg_replace( '/```([^`]+)```/', '<pre><code>$1</code></pre>', $content );
        
        // Convert inline code `code`
        $content = preg_replace( '/`([^`]+)`/', '<code>$1</code>', $content );
        
        // Convert blockquotes
        $content = preg_replace( '/^>\s*(.+)$/m', '<blockquote>$1</blockquote>', $content );
        
        return $content;
    }
    
    /**
     * Wrap consecutive list items in proper ul/ol tags
     */
    private static function wrap_lists( $content ) {
        // Wrap consecutive <li> items in <ul> tags
        $content = preg_replace( '/(<li>.*?<\/li>(?:\s*<li>.*?<\/li>)*)/s', '<ul>$1</ul>', $content );
        
        // Clean up any nested ul tags
        $content = preg_replace( '/<\/ul>\s*<ul>/', '', $content );
        
        return $content;
    }
    
    /**
     * Enhance content formatting
     */
    private static function enhance_formatting( $content ) {
        // Convert paragraphs (double line breaks to <p> tags)
        $paragraphs = explode( "\n\n", $content );
        $formatted_paragraphs = [];
        
        foreach ( $paragraphs as $paragraph ) {
            $paragraph = trim( $paragraph );
            if ( empty( $paragraph ) ) {
                continue;
            }
            
            // Skip if already wrapped in HTML tags
            if ( preg_match( '/^<(h[1-6]|ul|ol|blockquote|pre|div)/', $paragraph ) ) {
                $formatted_paragraphs[] = $paragraph;
            } else {
                // Convert single line breaks to <br> within paragraphs
                $paragraph = str_replace( "\n", "<br>", $paragraph );
                $formatted_paragraphs[] = '<p>' . $paragraph . '</p>';
            }
        }
        
        $content = implode( "\n\n", $formatted_paragraphs );
        
        // Ensure headings are properly formatted and bold
        $content = preg_replace( '/<h([1-6])>(?!<strong>)([^<]+)<\/h[1-6]>/', '<h$1><strong>$2</strong></h$1>', $content );
        
        // Add spacing around headings
        $content = preg_replace( '/(<h[1-6]><strong>.+?<\/strong><\/h[1-6]>)/', "\n$1\n", $content );
        
        return $content;
    }
    
    /**
     * Process internal link placeholders
     */
    private static function process_internal_links( $content ) {
        // Replace [INTERNAL LINK: topic] placeholders with actual links
        $content = preg_replace_callback(
            '/\[INTERNAL LINK:\s*([^\]]+)\]/',
            function( $matches ) {
                $topic = trim( $matches[1] );
                // Try to find a related post
                $related_post = get_posts([
                    's' => $topic,
                    'numberposts' => 1,
                    'post_status' => 'publish'
                ]);
                
                if ( $related_post ) {
                    return '<a href="' . get_permalink( $related_post[0]->ID ) . '">' . $topic . '</a>';
                }
                
                return '<strong>' . $topic . '</strong>'; // Return as bold text if no related post found
            },
            $content
        );
        
        return $content;
    }
    
    /**
     * Final cleanup and validation
     */
    private static function final_cleanup( $content ) {
        // Remove any remaining markdown syntax that wasn't converted
        $content = preg_replace( '/#{1,6}\s*\*\*/', '', $content );
        $content = preg_replace( '/\*\*\s*$/', '', $content );
        
        // Clean up excessive whitespace
        $content = preg_replace( '/\n{3,}/', "\n\n", $content );
        
        // Ensure proper spacing around block elements
        $content = preg_replace( '/(<\/(?:h[1-6]|p|ul|ol|blockquote|pre|div)>)\s*(<(?:h[1-6]|p|ul|ol|blockquote|pre|div))/', "$1\n\n$2", $content );
        
        // Remove empty paragraphs
        $content = preg_replace( '/<p>\s*<\/p>/', '', $content );
        
        // Sanitize the content for WordPress
        $content = wp_kses_post( $content );
        
        // Final trim
        $content = trim( $content );
        
        return $content;
    }
    
    /**
     * Process FAQ sections specifically
     */
    public static function process_faq_section( $content ) {
        // Look for FAQ patterns and format them nicely
        $content = preg_replace( '/\*\*Q:\s*(.+?)\*\*/', '<h4><strong>Q: $1</strong></h4>', $content );
        $content = preg_replace( '/\*\*A:\s*(.+?)\*\*/', '<p><strong>A:</strong> $1</p>', $content );
        
        // Alternative FAQ patterns
        $content = preg_replace( '/Q:\s*(.+?)$/m', '<h4><strong>Q: $1</strong></h4>', $content );
        $content = preg_replace( '/A:\s*(.+?)$/m', '<p><strong>A:</strong> $1</p>', $content );
        
        return $content;
    }
    
    /**
     * Add schema markup for better SEO
     */
    public static function add_schema_markup( $content, $topic, $category ) {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $topic,
            'articleSection' => $category,
            'datePublished' => current_time( 'c' ),
            'author' => [
                '@type' => 'Organization',
                'name' => get_bloginfo( 'name' )
            ]
        ];
        
        $schema_json = '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>';
        
        return $content . "\n\n" . $schema_json;
    }
    
    /**
     * Debug function to show before/after content processing
     */
    public static function debug_content_processing( $raw_content ) {
        if ( ! WP_DEBUG ) {
            return;
        }
        
        error_log( '[AIBPG][Content Processor] Raw content length: ' . strlen( $raw_content ) );
        error_log( '[AIBPG][Content Processor] Raw content preview: ' . substr( $raw_content, 0, 200 ) );
        
        $processed = self::process_content( $raw_content );
        
        error_log( '[AIBPG][Content Processor] Processed content length: ' . strlen( $processed ) );
        error_log( '[AIBPG][Content Processor] Processed content preview: ' . substr( $processed, 0, 200 ) );
    }
}
