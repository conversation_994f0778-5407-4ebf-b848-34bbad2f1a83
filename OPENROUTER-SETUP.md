# OpenRouter Integration Setup Guide

The AI Blog Post Generator now supports **OpenRouter** - providing access to multiple free AI models without requiring expensive API subscriptions!

## Why OpenRouter?

- **FREE Models Available**: Multiple models with generous free tiers
- **No Credit Card Required**: Get started immediately
- **Multiple Model Options**: Fallback between different models for reliability
- **Better Reliability**: If one model is down, automatically try another

## Quick Setup (5 minutes)

### Step 1: Get Your FREE OpenRouter API Key

1. Visit [OpenRouter.ai](https://openrouter.ai)
2. Click "Sign Up" (no credit card required)
3. Go to [API Keys](https://openrouter.ai/keys)
4. Click "Create Key"
5. Copy your API key (starts with `sk-or-`)

### Step 2: Configure the Plugin

1. Go to WordPress Admin → AI Blog Post Generator → Settings
2. Select **"OpenRouter (Free Models)"** as your AI Provider
3. Paste your API key in the "OpenRouter API Key" field
4. Choose a model from the dropdown (free models are marked in green)
5. Click "Test Connection" to verify it works
6. Save your settings

### Step 3: Start Generating!

- Go to "Generate Post" and click "Generate New Post"
- Or wait for automatic generation based on your schedule

## Available Free Models

### Mistral 7B Instruct (Recommended)
- **Model ID**: `mistralai/mistral-7b-instruct:free`
- **Best For**: General content, fast generation
- **Max Tokens**: 4,096

### DeepSeek R1 0528
- **Model ID**: `deepseek/deepseek-r1-0528:free`
- **Best For**: Advanced reasoning, complex topics
- **Max Tokens**: 4,096

### Gemma 7B IT
- **Model ID**: `google/gemma-7b-it:free`
- **Best For**: Instruction following, structured content
- **Max Tokens**: 4,096

### Llama 3.1 8B Instruct
- **Model ID**: `meta-llama/llama-3.1-8b-instruct:free`
- **Best For**: High-quality content, latest model
- **Max Tokens**: 4,096

## Features

### Automatic Fallback
If your selected model is unavailable, the plugin automatically tries other free models.

### Model Information
The admin interface shows:
- Model description and capabilities
- Maximum token limits
- Free vs paid status
- Real-time model information

### Enhanced Error Handling
- Retry logic with exponential backoff
- Rate limit handling
- Model-specific error recovery
- Detailed error logging

## Troubleshooting

### "API connection failed"
1. Verify your API key starts with `sk-or-`
2. Check you have credits/quota remaining
3. Try a different model from the dropdown
4. Check the error logs for specific details

### "Model not available"
1. The plugin will automatically try fallback models
2. Select a different model manually
3. Free models may have usage limits

### "Rate limit exceeded"
1. Free models have rate limits
2. Wait a few minutes and try again
3. Consider upgrading to paid tier for higher limits

## Cost Comparison

| Provider | Cost per 1K tokens | Monthly for daily posts |
|----------|-------------------|------------------------|
| OpenRouter (Free) | $0.00 | $0.00 |
| OpenAI GPT-3.5 | ~$0.002 | ~$3.00 |
| Google Gemini | ~$0.001 | ~$1.50 |

## Advanced Configuration

### Custom Model Selection
You can programmatically change models:
```php
AIBPG_Settings::update('openrouter_model', 'deepseek/deepseek-r1-0528:free');
```

### API Usage Monitoring
Check your usage at [OpenRouter Dashboard](https://openrouter.ai/activity)

## Migration from OpenAI/Gemini

1. Keep your existing API keys (for backup)
2. Change AI Provider to "OpenRouter"
3. Add OpenRouter API key
4. Test generation
5. Update your scheduling if needed

Your existing content templates and settings will work unchanged!

## Support

- **OpenRouter Issues**: [OpenRouter Discord](https://discord.gg/openrouter)
- **Plugin Issues**: Check the plugin's error logs
- **Model Status**: [OpenRouter Status](https://status.openrouter.ai)

## Tips for Best Results

1. **Start with Mistral 7B**: Most reliable free model
2. **Monitor Logs**: Check for any generation issues
3. **Use Fallbacks**: Enable multiple models for reliability
4. **Test Different Models**: Each has different strengths
5. **Check Quotas**: Monitor your usage limits

## What's Next?

The plugin now provides:
- ✅ Free AI model access
- ✅ Automatic model fallback
- ✅ Enhanced error handling
- ✅ Better reliability
- ✅ Cost savings

Start generating content for free with OpenRouter today!
