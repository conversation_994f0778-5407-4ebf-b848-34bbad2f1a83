<?php
/**
 * OpenAI provider implementation
 */
class AIBPG_OpenAI_Provider implements AIBPG_AI_Provider_Interface {
    private $api_key;
    private $endpoint = 'https://api.openai.com/v1/chat/completions';
    private $model = 'gpt-3.5-turbo';

    public function __construct() {
        $this->api_key = AIBPG_Settings::get('openai_api_key');
    }

    public function get_name() {
        return 'openai';
    }

    public function generate_content( $prompt, $options = [] ) {
        $response = $this->call_api( $prompt, $options );
        return $response['content'] ?? '';
    }

    public function generate_meta( $prompt, $options = [] ) {
        $meta_prompt = 'Generate a professional SEO title and a plain text meta description (max 155 chars, no HTML) for: ' . $prompt;
        $response = $this->call_api( $meta_prompt, $options );
        $lines = explode("\n", $response['content'] ?? '');
        return [
            'title' => trim($lines[0] ?? ''),
            'description' => trim($lines[1] ?? ''),
        ];
    }

    private function call_api( $prompt, $options = [] ) {
        $body = [
            'model' => $this->model,
            'messages' => [
                [ 'role' => 'system', 'content' => 'You are a helpful, professional blog writer.' ],
                [ 'role' => 'user', 'content' => $prompt ],
            ],
            'temperature' => $options['temperature'] ?? 0.7,
        ];

        // Only add max_tokens if specifically requested
        if ( isset( $options['max_tokens'] ) && $options['max_tokens'] > 0 ) {
            $body['max_tokens'] = $options['max_tokens'];
        }
        // Otherwise, let the model use its natural limits
        $args = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type'  => 'application/json',
            ],
            'body' => wp_json_encode($body),
            'timeout' => 30,
        ];
        $retries = 2;
        while ($retries >= 0) {
            $response = wp_remote_post($this->endpoint, $args);
            if (is_wp_error($response)) {
                $retries--;
                sleep(1);
                continue;
            }
            $data = json_decode(wp_remote_retrieve_body($response), true);
            if (isset($data['choices'][0]['message']['content'])) {
                return ['content' => trim($data['choices'][0]['message']['content'])];
            }
            $retries--;
            sleep(1);
        }
        error_log('[AIBPG][OpenAI] API call failed for prompt: ' . $prompt);
        return [];
    }
} 