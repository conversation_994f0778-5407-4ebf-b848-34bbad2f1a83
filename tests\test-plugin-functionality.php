<?php
/**
 * Basic functionality tests for AI Blog Post Generator
 * 
 * This is a simple test file to verify core plugin functionality.
 * For production use, consider implementing PHPUnit tests.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_Basic_Tests {
    
    public static function run_all_tests() {
        echo "<h2>AI Blog Post Generator - Basic Functionality Tests</h2>";
        
        $tests = [
            'test_plugin_activation' => 'Plugin Activation',
            'test_settings_class' => 'Settings Class',
            'test_topic_manager' => 'Topic Manager',
            'test_content_templates' => 'Content Templates',
            'test_security_validation' => 'Security Validation',
            'test_analytics' => 'Analytics System',
            'test_admin_pages' => 'Admin Pages'
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $method => $name) {
            echo "<h3>Testing: {$name}</h3>";
            
            try {
                $result = self::$method();
                if ($result) {
                    echo "<p style='color: green;'>✓ PASSED</p>";
                    $passed++;
                } else {
                    echo "<p style='color: red;'>✗ FAILED</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ ERROR: " . $e->getMessage() . "</p>";
            }
            
            echo "<hr>";
        }
        
        echo "<h3>Test Summary</h3>";
        echo "<p><strong>{$passed}/{$total} tests passed</strong></p>";
        
        if ($passed === $total) {
            echo "<p style='color: green; font-weight: bold;'>All tests passed! Plugin is ready to use.</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>Some tests failed. Please check the configuration.</p>";
        }
    }
    
    private static function test_plugin_activation() {
        // Test if main classes are loaded
        $required_classes = [
            'AIBPG_Main',
            'AIBPG_Settings',
            'AIBPG_Admin',
            'AIBPG_Generator',
            'AIBPG_Topic_Manager',
            'AIBPG_Content_Templates',
            'AIBPG_Analytics',
            'AIBPG_Security'
        ];
        
        foreach ($required_classes as $class) {
            if (!class_exists($class)) {
                echo "<p>Missing class: {$class}</p>";
                return false;
            }
        }
        
        // Test if constants are defined
        $required_constants = ['AIBPG_PLUGIN_DIR', 'AIBPG_PLUGIN_URL', 'AIBPG_VERSION'];
        foreach ($required_constants as $constant) {
            if (!defined($constant)) {
                echo "<p>Missing constant: {$constant}</p>";
                return false;
            }
        }
        
        echo "<p>All required classes and constants loaded successfully.</p>";
        return true;
    }
    
    private static function test_settings_class() {
        // Test settings retrieval
        $settings = AIBPG_Settings::all();
        
        if (!is_array($settings)) {
            echo "<p>Settings::all() should return an array</p>";
            return false;
        }
        
        // Test required settings keys
        $required_keys = ['ai_provider', 'post_status', 'post_frequency', 'categories', 'languages'];
        foreach ($required_keys as $key) {
            if (!array_key_exists($key, $settings)) {
                echo "<p>Missing settings key: {$key}</p>";
                return false;
            }
        }
        
        // Test individual setting retrieval
        $ai_provider = AIBPG_Settings::get('ai_provider');
        if (!in_array($ai_provider, ['openai', 'gemini'])) {
            echo "<p>Invalid default AI provider: {$ai_provider}</p>";
            return false;
        }
        
        echo "<p>Settings class working correctly with default values.</p>";
        return true;
    }
    
    private static function test_topic_manager() {
        // Test available categories
        $categories = AIBPG_Topic_Manager::get_available_categories();
        
        if (!is_array($categories) || empty($categories)) {
            echo "<p>No categories available</p>";
            return false;
        }
        
        $expected_categories = ['tech', 'health', 'business', 'lifestyle', 'education'];
        foreach ($expected_categories as $category) {
            if (!in_array($category, $categories)) {
                echo "<p>Missing category: {$category}</p>";
                return false;
            }
        }
        
        // Test topic retrieval for each category
        foreach ($categories as $category) {
            $topics = AIBPG_Topic_Manager::get_all_topics($category);
            if (!is_array($topics) || empty($topics)) {
                echo "<p>No topics available for category: {$category}</p>";
                return false;
            }
            
            if (count($topics) < 5) {
                echo "<p>Insufficient topics for category {$category}: " . count($topics) . "</p>";
                return false;
            }
        }
        
        // Test topic selection
        $topic = AIBPG_Topic_Manager::get_next_topic('tech');
        if (empty($topic)) {
            echo "<p>Failed to get next topic for tech category</p>";
            return false;
        }
        
        echo "<p>Topic Manager working correctly with " . count($categories) . " categories.</p>";
        return true;
    }
    
    private static function test_content_templates() {
        // Test content prompt generation
        $topic = "Test Topic";
        $category = "tech";
        $language = "en";
        
        $content_prompt = AIBPG_Content_Templates::get_content_prompt($topic, $category, $language);
        
        if (empty($content_prompt)) {
            echo "<p>Content prompt generation failed</p>";
            return false;
        }
        
        if (strpos($content_prompt, $topic) === false) {
            echo "<p>Content prompt doesn't contain the topic</p>";
            return false;
        }
        
        // Test meta prompt generation
        $meta_prompt = AIBPG_Content_Templates::get_meta_prompt($topic, $category, $language);
        
        if (empty($meta_prompt)) {
            echo "<p>Meta prompt generation failed</p>";
            return false;
        }
        
        if (strpos($meta_prompt, $topic) === false) {
            echo "<p>Meta prompt doesn't contain the topic</p>";
            return false;
        }
        
        // Test different categories
        $test_categories = ['health', 'business', 'lifestyle', 'education'];
        foreach ($test_categories as $test_category) {
            $prompt = AIBPG_Content_Templates::get_content_prompt($topic, $test_category, $language);
            if (empty($prompt)) {
                echo "<p>Content prompt failed for category: {$test_category}</p>";
                return false;
            }
        }
        
        echo "<p>Content Templates working correctly for all categories.</p>";
        return true;
    }
    
    private static function test_security_validation() {
        // Test topic validation
        $valid_topic = "Valid Topic Name";
        $validated = AIBPG_Security::validate_topic($valid_topic);
        
        if (is_wp_error($validated)) {
            echo "<p>Valid topic rejected: " . $validated->get_error_message() . "</p>";
            return false;
        }
        
        // Test invalid topic
        $invalid_topic = "<script>alert('xss')</script>";
        $validated = AIBPG_Security::validate_topic($invalid_topic);
        
        if (!is_wp_error($validated)) {
            echo "<p>Invalid topic accepted</p>";
            return false;
        }
        
        // Test category validation
        $valid_category = "tech";
        $validated = AIBPG_Security::validate_category($valid_category);
        
        if (is_wp_error($validated)) {
            echo "<p>Valid category rejected: " . $validated->get_error_message() . "</p>";
            return false;
        }
        
        // Test invalid category
        $invalid_category = "invalid_category";
        $validated = AIBPG_Security::validate_category($invalid_category);
        
        if (!is_wp_error($validated)) {
            echo "<p>Invalid category accepted</p>";
            return false;
        }
        
        echo "<p>Security validation working correctly.</p>";
        return true;
    }
    
    private static function test_analytics() {
        // Test dashboard stats retrieval
        $stats = AIBPG_Analytics::get_dashboard_stats();
        
        if (!is_array($stats)) {
            echo "<p>Dashboard stats should return an array</p>";
            return false;
        }
        
        $required_stats = ['total_posts', 'posts_this_month', 'success_rate', 'category_breakdown'];
        foreach ($required_stats as $stat) {
            if (!array_key_exists($stat, $stats)) {
                echo "<p>Missing dashboard stat: {$stat}</p>";
                return false;
            }
        }
        
        // Test category performance
        $category_performance = AIBPG_Analytics::get_category_performance();
        if (!is_array($category_performance)) {
            echo "<p>Category performance should return an array</p>";
            return false;
        }
        
        echo "<p>Analytics system working correctly.</p>";
        return true;
    }
    
    private static function test_admin_pages() {
        // Test if admin class methods exist
        $required_methods = [
            'dashboard_page',
            'settings_page',
            'generate_page',
            'logs_page',
            'status_page',
            'security_page'
        ];
        
        foreach ($required_methods as $method) {
            if (!method_exists('AIBPG_Admin', $method)) {
                echo "<p>Missing admin method: {$method}</p>";
                return false;
            }
        }
        
        // Test if menu is properly registered (this would need to be tested in admin context)
        if (!has_action('admin_menu', ['AIBPG_Admin', 'add_menu'])) {
            echo "<p>Admin menu not properly registered</p>";
            return false;
        }
        
        echo "<p>Admin pages and methods available.</p>";
        return true;
    }
}

// Only run tests if accessed directly by admin
if (current_user_can('manage_options') && isset($_GET['run_aibpg_tests'])) {
    AIBPG_Basic_Tests::run_all_tests();
}
