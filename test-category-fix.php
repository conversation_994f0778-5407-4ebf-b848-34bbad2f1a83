<?php
/**
 * Test the category normalization fix
 * Run this to verify the category mapping works correctly
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>Testing Category Normalization Fix</h2>";

// Test the normalize_category method
$test_categories = [
    'entertainment-news',
    'entertainment_news', 
    'news',
    'movies',
    'tv',
    'music',
    'gaming',
    'celebrity',
    'tech-news',
    'technology',
    'science',
    'health-news',
    'wellness',
    'fitness',
    'business-news',
    'finance',
    'marketing',
    'startup',
    'life',
    'travel',
    'food',
    'fashion',
    'learning',
    'school',
    'university',
    'training',
    'invalid-category',
    'tech', // Valid category
    'health', // Valid category
    'entertainment' // Valid category
];

$available_categories = AIBPG_Topic_Manager::get_available_categories();

echo "<h3>Available Categories:</h3>";
echo "<p>" . implode(', ', array_map('ucfirst', $available_categories)) . "</p>";

echo "<h3>Category Mapping Tests:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Input Category</th><th>Mapped Category</th><th>Status</th><th>Topics Available</th></tr>";

// Use reflection to test the private normalize_category method
$reflection = new ReflectionClass( 'AIBPG_Generator' );
$method = $reflection->getMethod( 'normalize_category' );
$method->setAccessible( true );

foreach ( $test_categories as $test_category ) {
    $normalized = $method->invoke( null, $test_category, $available_categories );
    
    // Check if topics are available for this category
    $topics = AIBPG_Topic_Manager::get_all_topics( $normalized );
    $topic_count = count( $topics );
    
    $status = '';
    if ( $test_category === $normalized ) {
        $status = in_array( $test_category, $available_categories ) ? '✅ Valid' : '❌ Invalid';
    } else {
        $status = '🔄 Mapped';
    }
    
    echo "<tr>";
    echo "<td><strong>{$test_category}</strong></td>";
    echo "<td><strong>{$normalized}</strong></td>";
    echo "<td>{$status}</td>";
    echo "<td>{$topic_count} topics</td>";
    echo "</tr>";
}

echo "</table>";

// Test topic generation for each available category
echo "<h3>Topic Generation Test:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Category</th><th>Sample Topic</th><th>Total Topics</th></tr>";

foreach ( $available_categories as $category ) {
    $topics = AIBPG_Topic_Manager::get_all_topics( $category );
    $sample_topic = !empty( $topics ) ? $topics[0] : 'No topics available';
    $topic_count = count( $topics );
    
    echo "<tr>";
    echo "<td><strong>" . ucfirst( $category ) . "</strong></td>";
    echo "<td>{$sample_topic}</td>";
    echo "<td>{$topic_count}</td>";
    echo "</tr>";
}

echo "</table>";

// Test content template availability
echo "<h3>Content Template Test:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Category</th><th>Template Available</th><th>Sample Prompt</th></tr>";

foreach ( $available_categories as $category ) {
    $sample_topic = "Sample Topic for " . ucfirst( $category );
    
    try {
        $prompt = AIBPG_Content_Templates::get_content_prompt( $sample_topic, $category, 'en' );
        $template_available = !empty( $prompt ) ? '✅ Yes' : '❌ No';
        $sample_prompt = substr( $prompt, 0, 100 ) . '...';
    } catch ( Exception $e ) {
        $template_available = '❌ Error';
        $sample_prompt = $e->getMessage();
    }
    
    echo "<tr>";
    echo "<td><strong>" . ucfirst( $category ) . "</strong></td>";
    echo "<td>{$template_available}</td>";
    echo "<td style='max-width: 300px; word-wrap: break-word;'>{$sample_prompt}</td>";
    echo "</tr>";
}

echo "</table>";

// Test current settings
echo "<h3>Current Plugin Settings:</h3>";
$current_settings = AIBPG_Settings::all();

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$categories_status = '✅ Valid';
foreach ( $current_settings['categories'] ?? [] as $cat ) {
    if ( !in_array( $cat, $available_categories ) ) {
        $categories_status = '❌ Contains invalid categories';
        break;
    }
}

echo "<tr><td><strong>Content Categories</strong></td><td>" . implode(', ', $current_settings['categories'] ?? []) . "</td><td>{$categories_status}</td></tr>";
echo "<tr><td><strong>WordPress Categories</strong></td><td>" . implode(', ', $current_settings['wp_categories'] ?? []) . "</td><td>✅ Optional</td></tr>";
echo "<tr><td><strong>AI Provider</strong></td><td>" . ($current_settings['ai_provider'] ?? 'Not set') . "</td><td>ℹ️ Info</td></tr>";

echo "</table>";

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>Available Categories:</strong> " . count($available_categories) . " (" . implode(', ', array_map('ucfirst', $available_categories)) . ")</li>";
echo "<li><strong>Total Topics:</strong> " . array_sum(array_map(function($cat) { return count(AIBPG_Topic_Manager::get_all_topics($cat)); }, $available_categories)) . "</li>";
echo "<li><strong>Category Mapping:</strong> Handles " . count($test_categories) . " different variations</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>Run <a href='fix-categories.php'>fix-categories.php</a> to clean up any invalid categories in settings</li>";
echo "<li>Try generating a post manually to test the fix</li>";
echo "<li>Check that no more 'Invalid category' errors appear in logs</li>";
echo "<li>The 'entertainment-news' category should now map to 'entertainment'</li>";
echo "</ul>";
?>
