<?php
/**
 * Scheduler for AI Blog Post Generator
 */
class AIBPG_Scheduler {
    const CRON_HOOK = 'aibpg_generate_post_event';

    public static function init() {
        add_action( self::CRON_HOOK, [ __CLASS__, 'generate_post' ] );
    }

    public static function activate() {
        self::schedule_next_event();
    }

    public static function schedule_next_event() {
        // Clear existing schedule
        wp_clear_scheduled_hook( self::CRON_HOOK );

        // Get frequency, with fallback if Settings class not available yet
        $frequency = 'daily'; // Default
        if ( class_exists( 'AIBPG_Settings' ) ) {
            $frequency = AIBPG_Settings::get( 'post_frequency' ) ?: 'daily';
        }

        $schedule = $frequency === 'weekly' ? 'weekly' : 'daily';

        if ( ! wp_next_scheduled( self::CRON_HOOK ) ) {
            wp_schedule_event( time(), $schedule, self::CRON_HOOK );
        }
    }

    public static function deactivate() {
        wp_clear_scheduled_hook( self::CRON_HOOK );
    }

    public static function generate_post() {
        // Ensure WordPress admin functions are available for media handling
        if ( ! function_exists( 'wp_generate_attachment_metadata' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/image.php' );
        }
        if ( ! function_exists( 'download_url' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/file.php' );
        }
        if ( ! function_exists( 'media_handle_sideload' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/media.php' );
        }

        // Check if required classes are available
        if ( ! class_exists( 'AIBPG_Settings' ) || ! class_exists( 'AIBPG_Generator' ) ) {
            error_log( '[AIBPG][Scheduler] Required classes not loaded yet' );
            return false;
        }

        // Check if we should generate a post
        $settings = AIBPG_Settings::all();

        // Validate required settings
        if ( empty( $settings['ai_provider'] ) ||
             ( $settings['ai_provider'] === 'openai' && empty( $settings['openai_api_key'] ) ) ||
             ( $settings['ai_provider'] === 'gemini' && empty( $settings['gemini_api_key'] ) ) ||
             ( $settings['ai_provider'] === 'openrouter' && empty( $settings['openrouter_api_key'] ) ) ) {
            error_log( '[AIBPG][Scheduler] Cannot generate post: AI provider not properly configured' );
            return false;
        }

        $result = AIBPG_Generator::generate();

        // Reschedule next event in case frequency changed
        self::schedule_next_event();

        return $result;
    }

    public static function manual_generate() {
        return self::generate_post();
    }

    public static function get_next_scheduled() {
        return wp_next_scheduled( self::CRON_HOOK );
    }

    public static function is_scheduled() {
        return wp_next_scheduled( self::CRON_HOOK ) !== false;
    }
} 