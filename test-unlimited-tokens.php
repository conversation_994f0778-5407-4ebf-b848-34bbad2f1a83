<?php
/**
 * Test unlimited token generation
 * Run this to verify the token limits have been removed
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>Testing Unlimited Token Generation</h2>";

// Get current settings
$settings = AIBPG_Settings::all();

echo "<h3>Current Configuration:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";

echo "<tr><td><strong>AI Provider</strong></td><td>" . ($settings['ai_provider'] ?? 'Not set') . "</td></tr>";
echo "<tr><td><strong>OpenRouter Model</strong></td><td>" . ($settings['openrouter_model'] ?? 'Not set') . "</td></tr>";

$api_key_display = empty($settings['openrouter_api_key']) ? 'Not set' : 'Set (' . strlen($settings['openrouter_api_key']) . ' chars)';
echo "<tr><td><strong>OpenRouter API Key</strong></td><td>{$api_key_display}</td></tr>";

echo "</table>";

// Test model information
echo "<h3>Model Token Limits:</h3>";

if ( class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    $available_models = AIBPG_OpenRouter_Provider::get_available_models();
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Model</th><th>Max Tokens</th><th>Status</th></tr>";
    
    foreach ( $available_models as $model_id => $model_info ) {
        $current = $model_id === ($settings['openrouter_model'] ?? 'mistralai/mistral-7b-instruct:free') ? ' (CURRENT)' : '';
        $status = $model_info['max_tokens'] === 'unlimited' ? '✅ Unlimited' : '⚠️ Limited';
        
        echo "<tr>";
        echo "<td><strong>{$model_info['name']}{$current}</strong></td>";
        echo "<td>{$model_info['max_tokens']}</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $unlimited_count = count(array_filter($available_models, function($model) {
        return $model['max_tokens'] === 'unlimited';
    }));
    
    echo "<p><strong>Models with unlimited tokens:</strong> {$unlimited_count} / " . count($available_models) . "</p>";
    
} else {
    echo "<p style='color: red;'>❌ AIBPG_OpenRouter_Provider class not available</p>";
}

// Test actual generation with long prompt
echo "<h3>Long Content Generation Test:</h3>";

if ( !empty($settings['openrouter_api_key']) && class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    echo "<p>Testing with a comprehensive blog post prompt that should generate long content...</p>";
    
    $long_prompt = "Write a comprehensive, detailed blog post about 'The Future of Artificial Intelligence in Web Development'. Include:

1. A detailed introduction explaining what AI in web development means
2. Current state of AI tools in web development (at least 5 examples with explanations)
3. Benefits and advantages of using AI in web development (detailed explanations)
4. Challenges and limitations (detailed analysis)
5. Future predictions and trends (next 5-10 years)
6. Practical implementation guide for developers
7. Case studies of successful AI integration in web projects
8. Tools and resources for getting started
9. Best practices and recommendations
10. Conclusion with actionable next steps

Make this a comprehensive, professional blog post of at least 2000 words with detailed explanations, examples, and practical advice. Use proper markdown formatting with headings, bullet points, and emphasis.";

    try {
        $start_time = microtime(true);
        
        $provider = new AIBPG_OpenRouter_Provider();
        $generated_content = $provider->generate_content( $long_prompt );
        
        $end_time = microtime(true);
        $generation_time = round($end_time - $start_time, 2);
        
        if ( $generated_content ) {
            $word_count = str_word_count( $generated_content );
            $char_count = strlen( $generated_content );
            
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h4>✅ Generation Successful!</h4>";
            echo "<p><strong>Content Length:</strong> {$word_count} words, {$char_count} characters</p>";
            echo "<p><strong>Generation Time:</strong> {$generation_time} seconds</p>";
            echo "</div>";
            
            echo "<h4>Generated Content Preview (first 500 characters):</h4>";
            echo "<div style='border: 1px solid #ddd; padding: 15px; background: #f9f9f9; max-height: 300px; overflow-y: auto;'>";
            echo "<pre>" . esc_html( substr( $generated_content, 0, 500 ) ) . "...</pre>";
            echo "</div>";
            
            echo "<h4>Content Analysis:</h4>";
            echo "<ul>";
            echo "<li><strong>Word Count:</strong> {$word_count} words " . ($word_count > 1000 ? '✅ Long content generated' : '⚠️ Shorter than expected') . "</li>";
            echo "<li><strong>Character Count:</strong> {$char_count} characters</li>";
            echo "<li><strong>Estimated Tokens:</strong> ~" . round($char_count / 4) . " tokens (rough estimate)</li>";
            echo "<li><strong>Generation Time:</strong> {$generation_time} seconds</li>";
            echo "</ul>";
            
            // Check for markdown formatting
            $has_headings = preg_match('/#{1,6}\s/', $generated_content);
            $has_bold = preg_match('/\*\*.*?\*\*/', $generated_content);
            $has_lists = preg_match('/^[\*\-\+]\s/m', $generated_content) || preg_match('/^\d+\.\s/m', $generated_content);
            
            echo "<h4>Formatting Analysis:</h4>";
            echo "<ul>";
            echo "<li><strong>Headings:</strong> " . ($has_headings ? '✅ Present' : '❌ Missing') . "</li>";
            echo "<li><strong>Bold Text:</strong> " . ($has_bold ? '✅ Present' : '❌ Missing') . "</li>";
            echo "<li><strong>Lists:</strong> " . ($has_lists ? '✅ Present' : '❌ Missing') . "</li>";
            echo "</ul>";
            
            // Test if content was truncated
            $ends_abruptly = !preg_match('/\.\s*$/', trim($generated_content));
            echo "<h4>Completion Analysis:</h4>";
            echo "<ul>";
            echo "<li><strong>Content Completion:</strong> " . ($ends_abruptly ? '⚠️ May be truncated' : '✅ Appears complete') . "</li>";
            echo "</ul>";
            
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h4>❌ Generation Failed</h4>";
            echo "<p>No content was generated. Check your API key and model configuration.</p>";
            echo "</div>";
        }
        
    } catch ( Exception $e ) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>❌ Error During Generation</h4>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: orange;'>⚠️ OpenRouter not configured - cannot test generation</p>";
}

// Test connection with unlimited tokens
echo "<h3>Connection Test (Unlimited):</h3>";

if ( !empty($settings['openrouter_api_key']) && class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    try {
        $provider = new AIBPG_OpenRouter_Provider();
        $test_result = $provider->test_connection();
        
        if ( $test_result['success'] ) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h4>✅ Connection Test Successful</h4>";
            echo "<p><strong>Message:</strong> {$test_result['message']}</p>";
            echo "<p><strong>Model:</strong> {$test_result['model']}</p>";
            if ( isset( $test_result['response_preview'] ) ) {
                echo "<p><strong>Response:</strong> {$test_result['response_preview']}</p>";
            }
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h4>❌ Connection Test Failed</h4>";
            echo "<p><strong>Error:</strong> {$test_result['message']}</p>";
            echo "</div>";
        }
        
    } catch ( Exception $e ) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>❌ Connection Test Error</h4>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Cannot test connection - OpenRouter not configured</p>";
}

// Compare with previous limits
echo "<h3>Token Limit Comparison:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Aspect</th><th>Before</th><th>After</th><th>Improvement</th></tr>";
echo "<tr><td><strong>Default Token Limit</strong></td><td>1000 tokens</td><td>Unlimited</td><td>✅ No limit</td></tr>";
echo "<tr><td><strong>Model Max Tokens</strong></td><td>4096 tokens</td><td>Unlimited</td><td>✅ No limit</td></tr>";
echo "<tr><td><strong>API Call Limit</strong></td><td>min(1000, 4096)</td><td>No limit</td><td>✅ Full generation</td></tr>";
echo "<tr><td><strong>Content Length</strong></td><td>~800 words max</td><td>Unlimited</td><td>✅ Long articles</td></tr>";
echo "<tr><td><strong>Blog Post Quality</strong></td><td>Often truncated</td><td>Complete posts</td><td>✅ Better quality</td></tr>";
echo "</table>";

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>Token Limits Removed:</strong> " . (class_exists('AIBPG_OpenRouter_Provider') ? '✅ Yes' : '❌ No') . "</li>";
echo "<li><strong>Models Show Unlimited:</strong> " . (isset($unlimited_count) && $unlimited_count > 0 ? '✅ Yes' : '❌ No') . "</li>";
echo "<li><strong>Long Content Generation:</strong> " . (isset($word_count) && $word_count > 1000 ? '✅ Working' : '⚠️ Test needed') . "</li>";
echo "<li><strong>API Connection:</strong> " . (isset($test_result) && $test_result['success'] ? '✅ Working' : '⚠️ Check needed') . "</li>";
echo "</ul>";

echo "<h3>Expected Benefits:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Longer Blog Posts:</strong> Generate comprehensive articles without truncation</li>";
echo "<li>✅ <strong>Better Quality:</strong> Complete thoughts and conclusions</li>";
echo "<li>✅ <strong>More Detailed Content:</strong> In-depth explanations and examples</li>";
echo "<li>✅ <strong>No Artificial Limits:</strong> Let the AI generate naturally</li>";
echo "<li>✅ <strong>Better SEO:</strong> Longer, more comprehensive content</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>Generate a new blog post to see the improved length</li>";
echo "<li>Check that posts are no longer truncated mid-sentence</li>";
echo "<li>Monitor the quality and completeness of generated content</li>";
echo "<li>The AI should now generate complete, comprehensive blog posts</li>";
echo "</ul>";
?>
