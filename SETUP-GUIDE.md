# AI Blog Post Generator - Setup Guide

This guide will walk you through setting up the AI Blog Post Generator plugin step by step.

## Prerequisites

Before you begin, ensure you have:
- WordPress 5.0 or higher
- PHP 7.4 or higher
- Admin access to your WordPress site
- At least one AI provider API key (OpenAI or Gemini)

## Step 1: Plugin Installation

### Method 1: Upload Plugin Files
1. Download the plugin files
2. Upload the entire `ai-blog-post-generator` folder to `/wp-content/plugins/`
3. Go to WordPress Admin → Plugins
4. Find "AI Blog Post Generator" and click "Activate"

### Method 2: WordPress Admin Upload
1. Go to WordPress Admin → Plugins → Add New
2. Click "Upload Plugin"
3. Choose the plugin ZIP file
4. Click "Install Now" then "Activate"

## Step 2: Get API Keys

### OpenAI API Key (Recommended)

1. **Create OpenAI Account**
   - Visit [https://platform.openai.com](https://platform.openai.com)
   - Sign up or log in to your account

2. **Generate API Key**
   - Go to [API Keys page](https://platform.openai.com/api-keys)
   - Click "Create new secret key"
   - Give it a name (e.g., "WordPress Blog Generator")
   - Copy the key (starts with `sk-`)
   - **Important**: Save this key securely - you won't see it again!

3. **Add Billing Information**
   - Go to [Billing](https://platform.openai.com/account/billing)
   - Add a payment method
   - Set usage limits if desired

### Google Gemini API Key (Alternative)

1. **Access Google AI Studio**
   - Visit [https://makersuite.google.com](https://makersuite.google.com)
   - Sign in with your Google account

2. **Create API Key**
   - Click "Get API Key"
   - Click "Create API key in new project" or select existing project
   - Copy the generated key
   - Save it securely

### Pexels API Key (Optional - for images)

1. **Create Pexels Account**
   - Visit [https://www.pexels.com/api/](https://www.pexels.com/api/)
   - Sign up for a free account

2. **Generate API Key**
   - Go to your [API dashboard](https://www.pexels.com/api/new/)
   - Your API key will be displayed
   - Copy and save it

## Step 3: Plugin Configuration

### Access Plugin Settings
1. Go to WordPress Admin
2. Look for "AI Blog Post Generator" in the left menu
3. Click on it to access the dashboard

### Configure AI Provider
1. Click on "Settings" tab
2. Under "AI Provider Configuration":
   - Select your preferred AI provider (OpenAI or Gemini)
   - Paste your API key in the appropriate field
   - Click "Test Connection" to verify it works
   - You should see "✓ Connection successful!"

### Configure Image Provider (Optional)
1. If you want featured images:
   - Select "Pexels" as Image Provider
   - Paste your Pexels API key
2. If you don't want images:
   - Select "None (No Images)"

### Content Settings
1. Click on "Content Settings" tab
2. **Select Categories**:
   - Check the WordPress categories you want to use
   - If none selected, "tech" will be used as default
3. **Choose Languages**:
   - Select languages for content generation
   - Hold Ctrl/Cmd to select multiple
4. **Post Status**:
   - "Draft" - Posts created as drafts for review
   - "Publish" - Posts published immediately

### Scheduling Settings
1. Click on "Scheduling" tab
2. **Post Frequency**:
   - "Daily" - One post per day
   - "Weekly" - One post per week
3. View next scheduled generation time

### Save Settings
1. Click "Save Changes"
2. You should see "Settings saved successfully!"

## Step 4: Test the Plugin

### Manual Generation Test
1. Go to "Generate Post" page
2. Click "Generate New Post"
3. Wait for the process to complete
4. Check if post was created successfully

### Check Generated Post
1. Go to WordPress Admin → Posts
2. Look for the newly generated post
3. Verify it has:
   - Proper title and content
   - Featured image (if enabled)
   - Assigned to correct category

## Step 5: Monitor and Maintain

### Dashboard Monitoring
- Check the Dashboard regularly for statistics
- Monitor success rates and performance
- Review trending topics

### Log Monitoring
- Check "Logs" page for any errors
- Review "Security" page for security events
- Clear logs periodically to save space

### Performance Optimization
- Monitor API usage and costs
- Adjust generation frequency if needed
- Review and update categories/topics

## Troubleshooting

### Common Issues and Solutions

#### "API connection failed"
**Possible Causes:**
- Invalid API key format
- Insufficient API credits
- Network connectivity issues

**Solutions:**
1. Verify API key is correct and complete
2. Check your API provider account for credits/billing
3. Use "Test Connection" button to diagnose
4. Check WordPress error logs

#### "No topics available"
**Possible Causes:**
- All topics used for selected category
- No categories selected

**Solutions:**
1. Reset used topics in Topic Manager
2. Add custom topics
3. Select additional categories

#### Posts not generating automatically
**Possible Causes:**
- WordPress cron not working
- Plugin not properly scheduled
- API issues

**Solutions:**
1. Test manual generation first
2. Check WordPress cron status
3. Deactivate and reactivate plugin
4. Contact hosting provider about cron jobs

#### "Class not found" errors
**Solutions:**
1. Deactivate and reactivate plugin
2. Check file permissions
3. Clear any caching plugins
4. Verify all plugin files uploaded correctly

### Getting Help

1. **Check Logs**: Always check the Logs page first
2. **Test Manually**: Try manual generation to isolate issues
3. **Verify Settings**: Ensure all API keys are valid
4. **Check Requirements**: Verify PHP/WordPress versions
5. **Review Documentation**: Check README.md for detailed info

## Security Best Practices

### API Key Security
- Never share your API keys
- Regularly rotate API keys
- Monitor API usage for unusual activity
- Set usage limits on your API accounts

### WordPress Security
- Keep WordPress and plugins updated
- Use strong admin passwords
- Limit admin access
- Regular backups

### Plugin Security
- Monitor security logs regularly
- Review generated content before publishing
- Set appropriate user permissions
- Keep plugin updated

## Cost Management

### OpenAI Costs
- Monitor usage in OpenAI dashboard
- Set monthly spending limits
- Consider using GPT-3.5-turbo for cost efficiency
- Typical cost: ~$0.003 per post

### Optimization Tips
- Reduce generation frequency if needed
- Monitor which categories perform best
- Use draft mode to review before publishing
- Set up billing alerts

## Advanced Configuration

### Custom Topics
1. Go to Topic Manager (if available)
2. Add topics specific to your niche
3. Organize by category
4. Test with manual generation

### Content Customization
- Review generated content templates
- Adjust for your brand voice
- Add custom post-processing if needed

### Integration with Other Plugins
- SEO plugins (Yoast, RankMath) - automatically supported
- Social sharing plugins - will work with generated posts
- Email marketing - can trigger on new posts

## Maintenance Schedule

### Daily
- Check dashboard for any errors
- Review newly generated posts

### Weekly
- Review analytics and performance
- Check API usage and costs
- Clear old logs if needed

### Monthly
- Review and update topics
- Analyze category performance
- Update plugin if new version available
- Review security logs

## Success Tips

1. **Start Small**: Begin with one category and daily generation
2. **Monitor Quality**: Review first few posts carefully
3. **Customize Topics**: Add topics specific to your audience
4. **Track Performance**: Use analytics to optimize
5. **Stay Updated**: Keep plugin and API keys current

## Support

If you encounter issues not covered in this guide:
1. Check the plugin's error logs
2. Review the security logs
3. Test with different settings
4. Verify API key validity and credits
5. Check WordPress and PHP requirements

Remember: The plugin is designed to be a starting point for content. Always review and edit generated posts to match your brand voice and quality standards.
