<?php
/**
 * Test content formatting and markdown processing
 * Run this to verify the content processor works correctly
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>Testing Content Formatting Fix</h2>";

// Test markdown samples
$test_markdown_samples = [
    "Basic Heading" => "## **Introduction**\n\nThis is a paragraph with **bold text** and *italic text*.\n\n### **Subheading**\n\nAnother paragraph here.",
    
    "Complex Formatting" => "# **Main Title**\n\n## **Section 1**\n\nParagraph with **bold** and *italic* text.\n\n- Bullet point 1\n- Bullet point 2\n- Bullet point 3\n\n### **Subsection**\n\n1. Numbered item 1\n2. Numbered item 2\n3. Numbered item 3\n\n#### **FAQ Section**\n\n**Q: What is this?**\n**A:** This is a test answer.\n\n**Q: How does it work?**\n**A:** It works by processing markdown.",
    
    "Links and Code" => "## **Technical Details**\n\nCheck out this [external link](https://example.com) and this [INTERNAL LINK: WordPress Tips].\n\nHere's some `inline code` and a code block:\n\n```\nfunction test() {\n    return 'Hello World';\n}\n```\n\n> This is a blockquote with important information.",
    
    "Real AI Output" => "### **Introduction**\n\nArtificial Intelligence (AI) has become **increasingly important** in modern technology. This comprehensive guide will explore:\n\n- Current AI trends\n- Implementation strategies  \n- Future developments\n\n## **What is AI?**\n\nAI refers to *machine intelligence* that can:\n\n1. Learn from data\n2. Make decisions\n3. Solve problems\n\n### **Benefits**\n\n**Key advantages include:**\n\n- Automation of tasks\n- Improved efficiency\n- Better decision making\n\n#### **FAQ**\n\n**Q: Is AI safe?**\n**A:** When properly implemented, AI can be very safe and beneficial."
];

echo "<h3>Content Processor Test Results:</h3>";

foreach ( $test_markdown_samples as $test_name => $markdown ) {
    echo "<div style='border: 1px solid #ddd; margin: 20px 0; padding: 15px;'>";
    echo "<h4>Test: {$test_name}</h4>";
    
    echo "<h5>Original Markdown:</h5>";
    echo "<pre style='background: #f5f5f5; padding: 10px; overflow-x: auto;'>" . esc_html( $markdown ) . "</pre>";
    
    if ( class_exists( 'AIBPG_Content_Processor' ) ) {
        $processed = AIBPG_Content_Processor::process_content( $markdown );
        
        echo "<h5>Processed HTML:</h5>";
        echo "<pre style='background: #e8f4f8; padding: 10px; overflow-x: auto;'>" . esc_html( $processed ) . "</pre>";
        
        echo "<h5>Rendered Output:</h5>";
        echo "<div style='border: 1px solid #ccc; padding: 15px; background: white;'>";
        echo $processed; // This will show how it actually renders
        echo "</div>";
        
        // Check for common issues
        $issues = [];
        if ( strpos( $processed, '**' ) !== false ) {
            $issues[] = "❌ Raw markdown bold syntax (**) still present";
        }
        if ( strpos( $processed, '##' ) !== false ) {
            $issues[] = "❌ Raw markdown heading syntax (##) still present";
        }
        if ( strpos( $processed, '<h2>' ) === false && strpos( $markdown, '##' ) !== false ) {
            $issues[] = "❌ H2 headings not converted properly";
        }
        if ( strpos( $processed, '<strong>' ) === false && strpos( $markdown, '**' ) !== false ) {
            $issues[] = "❌ Bold text not converted properly";
        }
        
        if ( empty( $issues ) ) {
            echo "<p style='color: green;'><strong>✅ All formatting converted correctly!</strong></p>";
        } else {
            echo "<div style='color: red;'>";
            echo "<strong>Issues found:</strong>";
            echo "<ul>";
            foreach ( $issues as $issue ) {
                echo "<li>{$issue}</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ AIBPG_Content_Processor class not available</p>";
    }
    
    echo "</div>";
}

// Test with actual AI generation if possible
echo "<h3>Live AI Generation Test:</h3>";

if ( class_exists( 'AIBPG_Settings' ) && class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    $settings = AIBPG_Settings::all();
    
    if ( !empty( $settings['openrouter_api_key'] ) ) {
        echo "<p>Testing with actual AI generation...</p>";
        
        try {
            $provider = new AIBPG_OpenRouter_Provider();
            $test_prompt = "Write a short blog post section about 'WordPress Tips' with markdown formatting. Use ## for headings, **bold** for emphasis, and bullet points. Keep it under 200 words.";
            
            $raw_ai_content = $provider->generate_content( $test_prompt );
            
            if ( $raw_ai_content ) {
                echo "<h4>Raw AI Output:</h4>";
                echo "<pre style='background: #f5f5f5; padding: 10px;'>" . esc_html( $raw_ai_content ) . "</pre>";
                
                $processed_ai = AIBPG_Content_Processor::process_content( $raw_ai_content );
                
                echo "<h4>Processed AI Output:</h4>";
                echo "<div style='border: 1px solid #ccc; padding: 15px; background: white;'>";
                echo $processed_ai;
                echo "</div>";
                
                echo "<p style='color: green;'>✅ Live AI generation and processing successful!</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ AI generation failed - check your OpenRouter settings</p>";
            }
            
        } catch ( Exception $e ) {
            echo "<p style='color: red;'>❌ Error during AI generation: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ OpenRouter API key not configured - skipping live test</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Required classes not available for live test</p>";
}

// Test content templates
echo "<h3>Content Template Test:</h3>";

if ( class_exists( 'AIBPG_Content_Templates' ) ) {
    $test_topic = "WordPress Security Best Practices";
    $test_category = "tech";
    $test_language = "en";
    
    try {
        $template_prompt = AIBPG_Content_Templates::get_content_prompt( $test_topic, $test_category, $test_language );
        
        echo "<h4>Generated Content Template Prompt:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>" . esc_html( substr( $template_prompt, 0, 1000 ) ) . "...</pre>";
        
        // Check if formatting instructions are included
        if ( strpos( $template_prompt, 'FORMATTING REQUIREMENTS' ) !== false ) {
            echo "<p style='color: green;'>✅ Formatting instructions included in template</p>";
        } else {
            echo "<p style='color: red;'>❌ Formatting instructions missing from template</p>";
        }
        
        if ( strpos( $template_prompt, 'markdown syntax' ) !== false ) {
            echo "<p style='color: green;'>✅ Markdown instructions included</p>";
        } else {
            echo "<p style='color: red;'>❌ Markdown instructions missing</p>";
        }
        
    } catch ( Exception $e ) {
        echo "<p style='color: red;'>❌ Error generating template: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ AIBPG_Content_Templates class not available</p>";
}

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>Content Processor:</strong> " . ( class_exists( 'AIBPG_Content_Processor' ) ? '✅ Available' : '❌ Missing' ) . "</li>";
echo "<li><strong>Content Templates:</strong> " . ( class_exists( 'AIBPG_Content_Templates' ) ? '✅ Available' : '❌ Missing' ) . "</li>";
echo "<li><strong>OpenRouter Provider:</strong> " . ( class_exists( 'AIBPG_OpenRouter_Provider' ) ? '✅ Available' : '❌ Missing' ) . "</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>If all tests show ✅, the content formatting fix is working</li>";
echo "<li>Try generating a new blog post to see the improved formatting</li>";
echo "<li>Check that headings are bold and no markdown syntax appears in posts</li>";
echo "<li>Verify that lists, links, and other formatting elements work correctly</li>";
echo "</ul>";

echo "<h3>Manual Test:</h3>";
echo "<p>To test manually:</p>";
echo "<ol>";
echo "<li>Go to AI Blog Post Generator → Generate Post</li>";
echo "<li>Click 'Generate New Post'</li>";
echo "<li>Check the generated post for:</li>";
echo "<ul>";
echo "<li>✅ Bold headings (H2, H3, H4)</li>";
echo "<li>✅ No visible ** or ## markdown syntax</li>";
echo "<li>✅ Proper bullet points and numbered lists</li>";
echo "<li>✅ Clean paragraph formatting</li>";
echo "</ul>";
echo "</ol>";
?>
