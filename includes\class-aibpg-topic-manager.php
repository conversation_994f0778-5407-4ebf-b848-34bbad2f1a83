<?php
/**
 * Topic management for unique topic cycling per category
 */
class AIBPG_Topic_Manager {
    private static $option_key = 'aibpg_used_topics';

    public static function get_all_topics( $category ) {
        $topics = [
            'tech' => [
                'AI and Machine Learning in 2024',
                'The Future of Robotics and Automation',
                'Quantum Computing Breakthroughs',
                '5G Networks and Their Impact',
                'Cybersecurity Best Practices',
                'Cloud Computing Trends',
                'Internet of Things (IoT) Applications',
                'Blockchain Technology Beyond Cryptocurrency',
                'Virtual and Augmented Reality',
                'Software Development Methodologies',
                'Mobile App Development Trends',
                'Web Development Frameworks',
                'Data Science and Analytics',
                'DevOps and Continuous Integration',
                'Open Source Software Benefits'
            ],
            'health' => [
                'Healthy Eating Habits for Busy People',
                'Mental Wellness and Stress Management',
                'Latest Fitness Trends and Workouts',
                'The Science of Better Sleep',
                'Preventive Healthcare Strategies',
                'Nutrition Myths Debunked',
                'Home Workout Equipment Guide',
                'Meditation and Mindfulness Practices',
                'Healthy Aging Tips',
                'Weight Management Strategies',
                'Heart Health and Exercise',
                'Immune System Boosting Foods',
                'Managing Chronic Conditions',
                'Mental Health Awareness',
                'Holistic Health Approaches'
            ],
            'business' => [
                'Small Business Marketing Strategies',
                'Remote Work Best Practices',
                'Entrepreneurship in the Digital Age',
                'Financial Planning for Startups',
                'Leadership Skills Development',
                'Customer Service Excellence',
                'E-commerce Growth Strategies',
                'Project Management Methodologies',
                'Team Building and Collaboration',
                'Business Process Optimization',
                'Digital Transformation Guide',
                'Sustainable Business Practices',
                'Innovation and Creativity',
                'Risk Management Strategies',
                'Performance Management Systems'
            ],
            'lifestyle' => [
                'Minimalist Living Guide',
                'Sustainable Living Practices',
                'Home Organization Tips',
                'Travel Planning and Budgeting',
                'Personal Development Strategies',
                'Time Management Techniques',
                'Hobby Ideas for Adults',
                'Fashion and Style Tips',
                'Home Improvement Projects',
                'Cooking and Recipe Ideas',
                'Gardening for Beginners',
                'Photography Tips and Tricks',
                'Reading and Book Recommendations',
                'Entertainment and Media Reviews',
                'Seasonal Activities and Celebrations'
            ],
            'education' => [
                'Online Learning Platforms Comparison',
                'Study Techniques for Better Retention',
                'Career Development and Skills',
                'Professional Certification Guide',
                'Language Learning Strategies',
                'Educational Technology Tools',
                'Adult Learning and Continuing Education',
                'STEM Education Importance',
                'Creative Writing and Communication',
                'Critical Thinking Development',
                'Research Methods and Techniques',
                'Academic Writing Tips',
                'Test Preparation Strategies',
                'Lifelong Learning Benefits',
                'Educational Psychology Insights'
            ],
            'entertainment' => [
                'Latest Movie Reviews and Analysis',
                'TV Show Recommendations and Reviews',
                'Music Industry Trends and News',
                'Celebrity News and Interviews',
                'Gaming Reviews and Guides',
                'Streaming Platform Comparisons',
                'Entertainment Industry Behind the Scenes',
                'Pop Culture Trends and Analysis',
                'Book Reviews and Recommendations',
                'Concert and Event Reviews',
                'Entertainment Technology and Innovation',
                'Social Media and Influencer Culture',
                'Comedy and Humor in Entertainment',
                'Entertainment History and Nostalgia',
                'Award Shows and Industry Recognition'
            ]
        ];

        return $topics[ $category ] ?? $topics['tech']; // Default to tech if category not found
    }

    public static function get_next_topic( $category ) {
        $all = self::get_all_topics_with_custom( $category );
        $used = self::get_used_topics( $category );
        $unused = array_diff( $all, $used );
        if ( empty( $unused ) ) {
            self::reset_used_topics( $category );
            $unused = $all;
        }
        if ( empty( $unused ) ) {
            return null; // No topics available
        }
        $topic = $unused[ array_rand( $unused ) ]; // Random selection instead of first
        self::mark_topic_used( $category, $topic );
        return $topic;
    }

    public static function get_used_topics( $category ) {
        $all_used = get_option( self::$option_key, [] );
        return $all_used[ $category ] ?? [];
    }

    public static function mark_topic_used( $category, $topic ) {
        $all_used = get_option( self::$option_key, [] );
        if ( ! isset( $all_used[ $category ] ) ) {
            $all_used[ $category ] = [];
        }
        if ( ! in_array( $topic, $all_used[ $category ] ) ) {
            $all_used[ $category ][] = $topic;
            update_option( self::$option_key, $all_used );
        }
    }

    public static function reset_used_topics( $category ) {
        $all_used = get_option( self::$option_key, [] );
        $all_used[ $category ] = [];
        update_option( self::$option_key, $all_used );
    }

    public static function add_custom_topic( $category, $topic ) {
        $custom_topics = get_option( 'aibpg_custom_topics', [] );
        if ( ! isset( $custom_topics[ $category ] ) ) {
            $custom_topics[ $category ] = [];
        }
        if ( ! in_array( $topic, $custom_topics[ $category ] ) ) {
            $custom_topics[ $category ][] = $topic;
            update_option( 'aibpg_custom_topics', $custom_topics );
            return true;
        }
        return false;
    }

    public static function get_custom_topics( $category ) {
        $custom_topics = get_option( 'aibpg_custom_topics', [] );
        return $custom_topics[ $category ] ?? [];
    }

    public static function get_all_topics_with_custom( $category ) {
        $default_topics = self::get_all_topics( $category );
        $custom_topics = self::get_custom_topics( $category );
        return array_merge( $default_topics, $custom_topics );
    }

    public static function remove_custom_topic( $category, $topic ) {
        $custom_topics = get_option( 'aibpg_custom_topics', [] );
        if ( isset( $custom_topics[ $category ] ) ) {
            $key = array_search( $topic, $custom_topics[ $category ] );
            if ( $key !== false ) {
                unset( $custom_topics[ $category ][ $key ] );
                $custom_topics[ $category ] = array_values( $custom_topics[ $category ] );
                update_option( 'aibpg_custom_topics', $custom_topics );
                return true;
            }
        }
        return false;
    }

    public static function get_available_categories() {
        return [ 'tech', 'health', 'business', 'lifestyle', 'education', 'entertainment' ];
    }
} 