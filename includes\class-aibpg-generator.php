<?php
/**
 * Main content generator for AI Blog Post Generator
 */
class AIBPG_Generator {
    public static function init() {
        // Placeholder for any generator-specific hooks
    }

    public static function generate() {
        $settings = AIBPG_Settings::all();

        // Validate and set category
        $category = $settings['categories'][0] ?? 'tech'; // Default to 'tech' if not set

        // Validate and normalize category
        $available_categories = AIBPG_Topic_Manager::get_available_categories();
        $original_category = $category;

        // Handle category variations and normalize
        $category = self::normalize_category( $category, $available_categories );

        if ( $category !== $original_category ) {
            self::log_error( "Invalid category '{$original_category}', using '{$category}' as fallback" );
        }

        $language = $settings['languages'][0] ?? 'en';

        // 1. Select unique topic
        $topic = AIBPG_Topic_Manager::get_next_topic( $category );
        if ( ! $topic ) {
            self::log_error( 'No topic available for category: ' . $category );
            return;
        }

        // 2. Get AI provider
        $ai_provider = self::get_ai_provider( $settings['ai_provider'], $settings );
        if ( ! $ai_provider ) {
            self::log_error( 'No valid AI provider selected.' );
            return;
        }

        // 3. Generate content
        $prompt = AIBPG_Content_Templates::get_content_prompt( $topic, $category, $language );
        $raw_content = $ai_provider->generate_content( $prompt );
        if ( ! $raw_content ) {
            self::log_error( 'AI failed to generate content for topic: ' . $topic );
            return false;
        }

        // Process the raw content to convert markdown to HTML
        $content = AIBPG_Content_Processor::process_content( $raw_content );
        if ( empty( $content ) ) {
            self::log_error( 'Content processing failed for topic: ' . $topic );
            return false;
        }

        // 4. Generate meta
        $meta_prompt = AIBPG_Content_Templates::get_meta_prompt( $topic, $category, $language );
        $meta = $ai_provider->generate_meta( $meta_prompt );
        if ( ! $meta || empty( $meta['title'] ) ) {
            self::log_error( 'AI failed to generate meta for topic: ' . $topic );
            return false;
        }

        // Validate and enhance meta
        $meta = self::validate_meta( $meta, $topic );

        // 5. Get image provider
        $image_provider = self::get_image_provider( $settings['image_provider'], $settings );
        $image_url = $image_provider ? $image_provider->fetch_image( $topic ) : '';

        // 6. Create post
        $post_data = [
            'post_title'   => $meta['title'],
            'post_content' => $content,
            'post_status'  => $settings['post_status'],
            'post_type'    => 'post',
            'post_author'  => get_current_user_id() ?: 1,
        ];

        // Handle WordPress categories for post assignment
        if ( !empty( $settings['wp_categories'] ) ) {
            $category_ids = [];
            foreach ( $settings['wp_categories'] as $wp_cat_slug ) {
                $wp_cat = get_category_by_slug( $wp_cat_slug );
                if ( $wp_cat ) {
                    $category_ids[] = $wp_cat->term_id;
                }
            }
            if ( !empty( $category_ids ) ) {
                $post_data['post_category'] = $category_ids;
            }
        }

        $post_id = wp_insert_post( $post_data );
        if ( is_wp_error( $post_id ) ) {
            self::log_error( 'Failed to create post: ' . $post_id->get_error_message() );
            return;
        }

        // 7. Set featured image if available
        if ( $image_url ) {
            self::set_featured_image( $post_id, $image_url );
        }

        // 8. Add meta description
        update_post_meta( $post_id, '_aioseo_description', $meta['description'] );

        // 9. Trigger action hook for extensibility
        do_action( 'aibpg_post_generated', $post_id, $topic, $category, $meta, $image_url );
    }

    private static function get_ai_provider( $provider, $settings ) {
        switch ( $provider ) {
            case 'openai':
                return new AIBPG_OpenAI_Provider();
            case 'gemini':
                return new AIBPG_Gemini_Provider();
            case 'openrouter':
                return new AIBPG_OpenRouter_Provider();
            default:
                // Default to OpenRouter if no valid provider specified
                return new AIBPG_OpenRouter_Provider();
        }
    }

    private static function get_image_provider( $provider, $settings ) {
        switch ( $provider ) {
            case 'pexels':
                return new AIBPG_Pexels_Provider();
            default:
                return null;
        }
    }

    private static function build_content_prompt( $topic, $category, $language ) {
        return "Write a professional, SEO-optimized blog post in {$language} about '{$topic}' for the {$category} category. Use clear headings, bullet points, short paragraphs, and a friendly tone. Include FAQ, checklist, and a call-to-action block.";
    }

    private static function set_featured_image( $post_id, $image_url ) {
        if ( empty( $image_url ) ) {
            return false;
        }

        // Include necessary WordPress files for media functions
        if ( ! function_exists( 'download_url' ) ) {
            if ( file_exists( ABSPATH . 'wp-admin/includes/file.php' ) ) {
                require_once( ABSPATH . 'wp-admin/includes/file.php' );
            }
        }
        if ( ! function_exists( 'media_handle_sideload' ) ) {
            if ( file_exists( ABSPATH . 'wp-admin/includes/media.php' ) ) {
                require_once( ABSPATH . 'wp-admin/includes/media.php' );
            }
        }
        if ( ! function_exists( 'wp_generate_attachment_metadata' ) ) {
            if ( file_exists( ABSPATH . 'wp-admin/includes/image.php' ) ) {
                require_once( ABSPATH . 'wp-admin/includes/image.php' );
            }
        }

        // If WordPress admin functions are still not available, skip image processing
        if ( ! function_exists( 'wp_generate_attachment_metadata' ) ) {
            self::log_error( 'WordPress admin functions not available, skipping image processing' );
            return false;
        }

        // Download image
        $image_data = wp_remote_get( $image_url, [ 'timeout' => 30 ] );
        if ( is_wp_error( $image_data ) ) {
            self::log_error( 'Failed to download image: ' . $image_data->get_error_message() );
            return false;
        }

        $image_body = wp_remote_retrieve_body( $image_data );
        if ( empty( $image_body ) ) {
            self::log_error( 'Empty image data received' );
            return false;
        }

        // Get file info
        $filename = basename( parse_url( $image_url, PHP_URL_PATH ) );
        if ( empty( $filename ) || strpos( $filename, '.' ) === false ) {
            $filename = 'featured-image-' . $post_id . '.jpg';
        }

        // Upload to WordPress
        $upload = wp_upload_bits( $filename, null, $image_body );
        if ( $upload['error'] ) {
            self::log_error( 'Failed to upload image: ' . $upload['error'] );
            return false;
        }

        // Create attachment
        $attachment = [
            'post_mime_type' => wp_check_filetype( $upload['file'] )['type'],
            'post_title'     => sanitize_file_name( $filename ),
            'post_content'   => '',
            'post_status'    => 'inherit'
        ];

        $attachment_id = wp_insert_attachment( $attachment, $upload['file'], $post_id );
        if ( is_wp_error( $attachment_id ) ) {
            self::log_error( 'Failed to create attachment: ' . $attachment_id->get_error_message() );
            return false;
        }

        // Generate metadata
        $attachment_data = wp_generate_attachment_metadata( $attachment_id, $upload['file'] );
        wp_update_attachment_metadata( $attachment_id, $attachment_data );

        // Set as featured image
        set_post_thumbnail( $post_id, $attachment_id );

        return $attachment_id;
    }

    private static function normalize_category( $category, $available_categories ) {
        // If category is already valid, return as-is
        if ( in_array( $category, $available_categories ) ) {
            return $category;
        }

        // Handle common variations and mappings
        $category_mappings = [
            'entertainment-news' => 'entertainment',
            'entertainment_news' => 'entertainment',
            'news' => 'entertainment',
            'movies' => 'entertainment',
            'tv' => 'entertainment',
            'music' => 'entertainment',
            'gaming' => 'entertainment',
            'celebrity' => 'entertainment',
            'tech-news' => 'tech',
            'technology' => 'tech',
            'science' => 'tech',
            'health-news' => 'health',
            'wellness' => 'health',
            'fitness' => 'health',
            'business-news' => 'business',
            'finance' => 'business',
            'marketing' => 'business',
            'startup' => 'business',
            'life' => 'lifestyle',
            'travel' => 'lifestyle',
            'food' => 'lifestyle',
            'fashion' => 'lifestyle',
            'learning' => 'education',
            'school' => 'education',
            'university' => 'education',
            'training' => 'education'
        ];

        // Check if we have a direct mapping
        if ( isset( $category_mappings[ $category ] ) ) {
            return $category_mappings[ $category ];
        }

        // Try to find partial matches
        foreach ( $available_categories as $available_cat ) {
            if ( strpos( $category, $available_cat ) !== false || strpos( $available_cat, $category ) !== false ) {
                return $available_cat;
            }
        }

        // Default fallback
        return 'tech';
    }

    private static function log_error( $message ) {
        error_log( '[AIBPG] ' . $message );
        do_action( 'aibpg_log_error', $message );
    }
} 