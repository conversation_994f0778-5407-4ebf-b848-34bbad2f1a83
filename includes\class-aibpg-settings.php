<?php
/**
 * Settings handler for AI Blog Post Generator
 */
class AIBPG_Settings {
    private static $options_key = 'aibpg_options';
    private static $defaults = [
        'openai_api_key' => '',
        'gemini_api_key' => '',
        'openrouter_api_key' => '',
        'openrouter_model' => 'mistralai/mistral-7b-instruct:free',
        'ai_provider' => 'openrouter',
        'image_provider' => 'pexels',
        'pexels_api_key' => '',
        'post_status' => 'draft',
        'post_frequency' => 'daily',
        'categories' => [],
        'wp_categories' => [],
        'languages' => ['en'],
    ];

    public static function init() {}

    public static function get( $key = null ) {
        $options = get_option( self::$options_key, self::$defaults );
        if ( $key ) {
            return isset( $options[ $key ] ) ? $options[ $key ] : ( self::$defaults[ $key ] ?? null );
        }
        return wp_parse_args( $options, self::$defaults );
    }

    public static function update( $key, $value ) {
        $options = self::get();
        $options[ $key ] = $value;
        update_option( self::$options_key, $options );
    }

    public static function all() {
        return self::get();
    }
} 