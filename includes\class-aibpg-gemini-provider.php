<?php
/**
 * Gemini provider implementation
 */
class AIBPG_Gemini_Provider implements AIBPG_AI_Provider_Interface {
    private $api_key;
    private $endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
    private $model = 'gemini-pro';

    public function __construct() {
        $this->api_key = AIBPG_Settings::get('gemini_api_key');
    }

    public function get_name() {
        return 'gemini';
    }

    public function generate_content( $prompt, $options = [] ) {
        $response = $this->call_api( $prompt, $options );
        return $response['content'] ?? '';
    }

    public function generate_meta( $prompt, $options = [] ) {
        $meta_prompt = 'Generate a professional SEO title and a plain text meta description (max 155 chars, no HTML) for: ' . $prompt;
        $response = $this->call_api( $meta_prompt, $options );
        $lines = explode("\n", $response['content'] ?? '');
        return [
            'title' => trim($lines[0] ?? ''),
            'description' => trim($lines[1] ?? ''),
        ];
    }

    private function call_api( $prompt, $options = [] ) {
        $body = [
            'contents' => [
                [ 'parts' => [ [ 'text' => $prompt ] ] ]
            ]
        ];
        $args = [
            'headers' => [
                'Content-Type'  => 'application/json',
            ],
            'body' => wp_json_encode($body),
            'timeout' => 30,
        ];
        $url = $this->endpoint . '?key=' . urlencode($this->api_key);
        $retries = 2;
        while ($retries >= 0) {
            $response = wp_remote_post($url, $args);
            if (is_wp_error($response)) {
                $retries--;
                sleep(1);
                continue;
            }
            $data = json_decode(wp_remote_retrieve_body($response), true);
            if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                return ['content' => trim($data['candidates'][0]['content']['parts'][0]['text'])];
            }
            $retries--;
            sleep(1);
        }
        error_log('[AIBPG][Gemini] API call failed for prompt: ' . $prompt);
        return [];
    }
} 