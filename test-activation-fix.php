<?php
/**
 * Test the plugin activation fix
 * Run this to verify the activation process works correctly
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>Testing Plugin Activation Fix</h2>";

// Test class availability
$required_classes = [
    'AIBPG_Main',
    'AIBPG_Settings', 
    'AIBPG_Scheduler',
    'AIBPG_Generator',
    'AIBPG_Topic_Manager',
    'AIBPG_OpenRouter_Provider',
    'AIBPG_Admin'
];

echo "<h3>Class Availability Test:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Class</th><th>Status</th><th>File Location</th></tr>";

foreach ( $required_classes as $class ) {
    $exists = class_exists( $class );
    $status = $exists ? '✅ Loaded' : '❌ Missing';
    
    $file_location = 'Unknown';
    if ( $exists ) {
        $reflection = new ReflectionClass( $class );
        $file_location = str_replace( AIBPG_PLUGIN_DIR, '', $reflection->getFileName() );
    }
    
    echo "<tr>";
    echo "<td><strong>{$class}</strong></td>";
    echo "<td>{$status}</td>";
    echo "<td>{$file_location}</td>";
    echo "</tr>";
}

echo "</table>";

// Test scheduler functionality
echo "<h3>Scheduler Test:</h3>";

if ( class_exists( 'AIBPG_Scheduler' ) ) {
    echo "<p>✅ AIBPG_Scheduler class is available</p>";
    
    // Test if cron is scheduled
    $next_scheduled = AIBPG_Scheduler::get_next_scheduled();
    if ( $next_scheduled ) {
        echo "<p>✅ Cron job is scheduled for: <strong>" . date( 'Y-m-d H:i:s', $next_scheduled ) . "</strong></p>";
    } else {
        echo "<p>⚠️ No cron job scheduled</p>";
        
        // Try to schedule it
        echo "<p>Attempting to schedule cron job...</p>";
        AIBPG_Scheduler::schedule_next_event();
        
        $next_scheduled = AIBPG_Scheduler::get_next_scheduled();
        if ( $next_scheduled ) {
            echo "<p>✅ Cron job scheduled successfully for: <strong>" . date( 'Y-m-d H:i:s', $next_scheduled ) . "</strong></p>";
        } else {
            echo "<p>❌ Failed to schedule cron job</p>";
        }
    }
    
    // Test scheduler methods
    $methods_to_test = [
        'activate',
        'deactivate', 
        'schedule_next_event',
        'get_next_scheduled',
        'is_scheduled'
    ];
    
    echo "<h4>Scheduler Methods:</h4>";
    echo "<ul>";
    foreach ( $methods_to_test as $method ) {
        $exists = method_exists( 'AIBPG_Scheduler', $method );
        $status = $exists ? '✅' : '❌';
        echo "<li>{$status} <strong>{$method}()</strong></li>";
    }
    echo "</ul>";
    
} else {
    echo "<p>❌ AIBPG_Scheduler class not available</p>";
}

// Test settings functionality
echo "<h3>Settings Test:</h3>";

if ( class_exists( 'AIBPG_Settings' ) ) {
    echo "<p>✅ AIBPG_Settings class is available</p>";
    
    try {
        $settings = AIBPG_Settings::all();
        echo "<p>✅ Settings retrieved successfully</p>";
        echo "<p><strong>AI Provider:</strong> " . ($settings['ai_provider'] ?? 'Not set') . "</p>";
        echo "<p><strong>Post Frequency:</strong> " . ($settings['post_frequency'] ?? 'Not set') . "</p>";
        echo "<p><strong>Categories:</strong> " . implode(', ', $settings['categories'] ?? []) . "</p>";
    } catch ( Exception $e ) {
        echo "<p>❌ Error retrieving settings: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ AIBPG_Settings class not available</p>";
}

// Test activation functions
echo "<h3>Activation Functions Test:</h3>";

if ( function_exists( 'aibpg_activate_plugin' ) ) {
    echo "<p>✅ aibpg_activate_plugin() function exists</p>";
} else {
    echo "<p>❌ aibpg_activate_plugin() function missing</p>";
}

if ( function_exists( 'aibpg_deactivate_plugin' ) ) {
    echo "<p>✅ aibpg_deactivate_plugin() function exists</p>";
} else {
    echo "<p>❌ aibpg_deactivate_plugin() function missing</p>";
}

// Test WordPress cron system
echo "<h3>WordPress Cron Test:</h3>";

$cron_jobs = wp_get_scheduled_event( 'aibpg_generate_post' );
if ( $cron_jobs ) {
    echo "<p>✅ WordPress cron job found</p>";
    echo "<p><strong>Hook:</strong> aibpg_generate_post</p>";
    echo "<p><strong>Next Run:</strong> " . date( 'Y-m-d H:i:s', $cron_jobs->timestamp ) . "</p>";
    echo "<p><strong>Schedule:</strong> " . $cron_jobs->schedule . "</p>";
} else {
    echo "<p>⚠️ No WordPress cron job found for 'aibpg_generate_post'</p>";
}

// List all scheduled events for debugging
$all_crons = wp_get_ready_cron_jobs();
$aibpg_crons = [];
foreach ( $all_crons as $timestamp => $cron ) {
    foreach ( $cron as $hook => $events ) {
        if ( strpos( $hook, 'aibpg' ) !== false ) {
            $aibpg_crons[] = [
                'hook' => $hook,
                'timestamp' => $timestamp,
                'time' => date( 'Y-m-d H:i:s', $timestamp )
            ];
        }
    }
}

if ( !empty( $aibpg_crons ) ) {
    echo "<h4>AIBPG Cron Jobs:</h4>";
    echo "<ul>";
    foreach ( $aibpg_crons as $cron ) {
        echo "<li><strong>{$cron['hook']}</strong> - {$cron['time']}</li>";
    }
    echo "</ul>";
}

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>Classes Loaded:</strong> " . count( array_filter( $required_classes, 'class_exists' ) ) . "/" . count( $required_classes ) . "</li>";
echo "<li><strong>Scheduler Available:</strong> " . ( class_exists( 'AIBPG_Scheduler' ) ? 'Yes' : 'No' ) . "</li>";
echo "<li><strong>Settings Available:</strong> " . ( class_exists( 'AIBPG_Settings' ) ? 'Yes' : 'No' ) . "</li>";
echo "<li><strong>Cron Scheduled:</strong> " . ( AIBPG_Scheduler::get_next_scheduled() ? 'Yes' : 'No' ) . "</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>If all tests show ✅, the activation fix is working</li>";
echo "<li>Try deactivating and reactivating the plugin to test the fix</li>";
echo "<li>Check that no more 'Class not found' errors occur during activation</li>";
echo "<li>The plugin should now activate without fatal errors</li>";
echo "</ul>";
?>
