<?php
/**
 * OpenRouter provider implementation for AI Blog Post Generator
 */
class AIBPG_OpenRouter_Provider implements AIBPG_AI_Provider_Interface {
    private $api_key;
    private $endpoint = 'https://openrouter.ai/api/v1/chat/completions';
    private $model;
    
    // Available free models on OpenRouter
    private static $available_models = [
        'mistralai/mistral-7b-instruct:free' => [
            'name' => 'Mistral 7B Instruct (Free)',
            'description' => 'Fast and efficient model for general tasks',
            'max_tokens' => 'unlimited',
            'free' => true
        ],
        'microsoft/wizardlm-2-8x22b' => [
            'name' => 'WizardLM 2 8x22B',
            'description' => 'High-quality model for complex reasoning',
            'max_tokens' => 'unlimited',
            'free' => false
        ],
        'deepseek/deepseek-r1-0528:free' => [
            'name' => 'DeepSeek R1 0528 (Free)',
            'description' => 'Advanced reasoning model with free tier',
            'max_tokens' => 'unlimited',
            'free' => true
        ],
        'google/gemma-7b-it:free' => [
            'name' => 'Gemma 7B IT (Free)',
            'description' => 'Google\'s instruction-tuned model',
            'max_tokens' => 'unlimited',
            'free' => true
        ],
        'meta-llama/llama-3.1-8b-instruct:free' => [
            'name' => 'Llama 3.1 8B Instruct (Free)',
            'description' => 'Meta\'s latest instruction-following model',
            'max_tokens' => 'unlimited',
            'free' => true
        ],
        'qwen/qwen-2.5-coder-32b-instruct' => [
            'name' => 'Qwen3 Coder',
            'description' => 'New free coding-focused model from Qwen with advanced programming capabilities',
            'max_tokens' => 'unlimited',
            'free' => true
        ],
        'google/gemma-2-2b-it:free' => [
            'name' => 'Gemma 3n E2B IT (Free)',
            'description' => 'Multimodal, instruction-tuned model by Google DeepMind. 2B effective params (6B architecture), MatFormer-based with Mix-and-Match framework, 32K context, optimized for low-resource deployment',
            'max_tokens' => 'unlimited',
            'free' => true
        ]
    ];

    public function __construct() {
        $this->api_key = AIBPG_Settings::get('openrouter_api_key');
        $this->model = AIBPG_Settings::get('openrouter_model') ?: 'mistralai/mistral-7b-instruct:free';
    }

    public function get_name() {
        return 'openrouter';
    }

    public static function get_available_models() {
        return self::$available_models;
    }

    public static function get_free_models() {
        return array_filter(self::$available_models, function($model) {
            return $model['free'] === true;
        });
    }

    public function generate_content( $prompt, $options = [] ) {
        $response = $this->call_api( $prompt, $options );
        return $response['content'] ?? '';
    }

    public function generate_meta( $prompt, $options = [] ) {
        $meta_prompt = 'Generate ONLY a professional SEO title and a plain text meta description (max 155 chars, no HTML) for: ' . $prompt . "\n\nFormat:\nTitle: [SEO optimized title]\nDescription: [meta description under 155 characters]";
        $response = $this->call_api( $meta_prompt, $options );
        $content = $response['content'] ?? '';
        
        // Parse the response
        $title = '';
        $description = '';
        
        if ( preg_match('/Title:\s*(.+)/i', $content, $title_matches) ) {
            $title = trim( $title_matches[1] );
        }
        
        if ( preg_match('/Description:\s*(.+)/i', $content, $desc_matches) ) {
            $description = trim( $desc_matches[1] );
        }
        
        // Fallback parsing if format not followed
        if ( empty( $title ) || empty( $description ) ) {
            $lines = array_filter( array_map( 'trim', explode( "\n", $content ) ) );
            $title = $lines[0] ?? $prompt;
            $description = $lines[1] ?? substr( $prompt, 0, 150 );
        }
        
        return [
            'title' => $title,
            'description' => substr( $description, 0, 155 ),
        ];
    }

    private function call_api( $prompt, $options = [] ) {
        if ( empty( $this->api_key ) ) {
            error_log( '[AIBPG][OpenRouter] API key not configured' );
            return [];
        }

        // Log the API call attempt (without exposing the full API key)
        $masked_key = substr( $this->api_key, 0, 8 ) . '...';
        error_log( "[AIBPG][OpenRouter] Making API call with key: {$masked_key}, model: {$this->model}" );

        // Build request body without token limits
        $body = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a helpful, professional blog writer who creates high-quality, SEO-optimized content.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 1.0,
            'frequency_penalty' => $options['frequency_penalty'] ?? 0.0,
            'presence_penalty' => $options['presence_penalty'] ?? 0.0
        ];

        // Only add max_tokens if specifically requested (for testing purposes)
        if ( isset( $options['max_tokens'] ) && $options['max_tokens'] > 0 ) {
            $body['max_tokens'] = $options['max_tokens'];
        }
        // Otherwise, let the model use its natural limits

        $args = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url(), // OpenRouter requires this
                'X-Title' => 'AI Blog Post Generator' // Optional but recommended
            ],
            'body' => wp_json_encode( $body ),
            'timeout' => 60, // Longer timeout for free models
            'method' => 'POST'
        ];

        $retries = 3;
        $fallback_models = array_keys( self::get_free_models() );
        
        while ( $retries >= 0 ) {
            $response = wp_remote_post( $this->endpoint, $args );
            
            if ( is_wp_error( $response ) ) {
                error_log( '[AIBPG][OpenRouter] HTTP Error: ' . $response->get_error_message() );
                
                // Try fallback model if available
                if ( $retries > 0 && !empty( $fallback_models ) ) {
                    $fallback_model = array_shift( $fallback_models );
                    if ( $fallback_model !== $this->model ) {
                        $body['model'] = $fallback_model;
                        $args['body'] = wp_json_encode( $body );
                        error_log( "[AIBPG][OpenRouter] Trying fallback model: {$fallback_model}" );
                    }
                }
                
                $retries--;
                if ( $retries >= 0 ) {
                    sleep( 2 );
                    continue;
                }
                return [];
            }

            $response_code = wp_remote_retrieve_response_code( $response );
            $data = json_decode( wp_remote_retrieve_body( $response ), true );
            
            if ( $response_code === 200 && isset( $data['choices'][0]['message']['content'] ) ) {
                return [ 'content' => trim( $data['choices'][0]['message']['content'] ) ];
            }
            
            // Handle specific OpenRouter errors
            if ( isset( $data['error'] ) ) {
                $error_message = $data['error']['message'] ?? 'Unknown error';
                $error_code = $data['error']['code'] ?? 'unknown';
                
                error_log( "[AIBPG][OpenRouter] API Error ({$error_code}): {$error_message}" );
                
                // Handle rate limiting
                if ( $error_code === 'rate_limit_exceeded' || $response_code === 429 ) {
                    $retry_after = wp_remote_retrieve_header( $response, 'retry-after' ) ?: 5;
                    if ( $retries > 0 ) {
                        sleep( min( $retry_after, 10 ) );
                        $retries--;
                        continue;
                    }
                }
                
                // Try fallback model for model-specific errors
                if ( in_array( $error_code, ['model_not_found', 'model_overloaded'] ) && $retries > 0 && !empty( $fallback_models ) ) {
                    $fallback_model = array_shift( $fallback_models );
                    if ( $fallback_model !== $this->model ) {
                        $body['model'] = $fallback_model;
                        $args['body'] = wp_json_encode( $body );
                        error_log( "[AIBPG][OpenRouter] Trying fallback model for error: {$fallback_model}" );
                        $retries--;
                        continue;
                    }
                }
                
                return [];
            }
            
            $retries--;
            if ( $retries >= 0 ) {
                sleep( 2 );
            }
        }
        
        error_log( '[AIBPG][OpenRouter] API call failed after all retries for prompt: ' . substr( $prompt, 0, 100 ) );
        return [];
    }

    public function test_connection() {
        if ( empty( $this->api_key ) ) {
            return [
                'success' => false,
                'message' => 'OpenRouter API key is not configured.',
                'model' => $this->model
            ];
        }

        // Test with a simple prompt and minimal tokens (only for connection testing)
        $test_prompt = "Say 'Hello' to test the connection.";

        // Use a simple direct API call for testing (with minimal tokens for speed)
        $body = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $test_prompt
                ]
            ],
            'max_tokens' => 50, // Small limit only for connection testing
            'temperature' => 0.1
        ];

        $args = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url(),
                'X-Title' => 'AI Blog Post Generator - Connection Test'
            ],
            'body' => wp_json_encode( $body ),
            'timeout' => 30,
            'method' => 'POST'
        ];

        $response = wp_remote_post( $this->endpoint, $args );

        if ( is_wp_error( $response ) ) {
            return [
                'success' => false,
                'message' => 'HTTP Error: ' . $response->get_error_message(),
                'model' => $this->model,
                'debug' => [
                    'endpoint' => $this->endpoint,
                    'error_code' => $response->get_error_code()
                ]
            ];
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        $response_body = wp_remote_retrieve_body( $response );
        $data = json_decode( $response_body, true );

        if ( $response_code === 200 && isset( $data['choices'][0]['message']['content'] ) ) {
            return [
                'success' => true,
                'message' => 'OpenRouter API connection successful!',
                'model' => $this->model,
                'response_preview' => trim( $data['choices'][0]['message']['content'] ),
                'debug' => [
                    'response_code' => $response_code,
                    'model_used' => $data['model'] ?? $this->model
                ]
            ];
        }

        // Handle specific error cases
        $error_message = 'Connection failed';
        $debug_info = [
            'response_code' => $response_code,
            'endpoint' => $this->endpoint,
            'model' => $this->model
        ];

        if ( isset( $data['error'] ) ) {
            $error = $data['error'];
            $error_message = $error['message'] ?? 'Unknown API error';
            $debug_info['api_error'] = $error;

            // Handle common errors
            if ( $response_code === 401 ) {
                $error_message = 'Invalid API key. Please check your OpenRouter API key.';
            } elseif ( $response_code === 429 ) {
                $error_message = 'Rate limit exceeded. Please try again later.';
            } elseif ( $response_code === 400 ) {
                $error_message = 'Bad request. Model might not be available: ' . $this->model;
            }
        } else {
            $debug_info['raw_response'] = substr( $response_body, 0, 500 );
        }

        return [
            'success' => false,
            'message' => $error_message,
            'model' => $this->model,
            'debug' => $debug_info
        ];
    }

    public function get_current_model_info() {
        return self::$available_models[$this->model] ?? null;
    }

    public function get_usage_stats() {
        // OpenRouter doesn't provide usage stats in the same way
        // This could be extended to track usage locally if needed
        return [
            'model' => $this->model,
            'model_info' => $this->get_current_model_info(),
            'endpoint' => $this->endpoint
        ];
    }
}
