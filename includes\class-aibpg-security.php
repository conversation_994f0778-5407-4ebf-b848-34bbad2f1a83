<?php
/**
 * Security and validation for AI Blog Post Generator
 */
class AIBPG_Security {
    
    public static function init() {
        add_action( 'init', [ __CLASS__, 'setup_security' ] );
        add_filter( 'aibpg_validate_api_key', [ __CLASS__, 'validate_api_key' ], 10, 2 );
        add_filter( 'aibpg_sanitize_content', [ __CLASS__, 'sanitize_generated_content' ] );
    }
    
    public static function setup_security() {
        // Add security headers
        if ( is_admin() && strpos( $_SERVER['REQUEST_URI'] ?? '', 'aibpg' ) !== false ) {
            header( 'X-Content-Type-Options: nosniff' );
            header( 'X-Frame-Options: DENY' );
            header( 'X-XSS-Protection: 1; mode=block' );
        }
    }
    
    public static function validate_nonce( $action, $nonce_field = 'nonce' ) {
        $nonce = $_POST[ $nonce_field ] ?? $_GET[ $nonce_field ] ?? '';
        
        if ( ! wp_verify_nonce( $nonce, $action ) ) {
            wp_die( 'Security check failed. Please refresh the page and try again.', 'Security Error', [ 'response' => 403 ] );
        }
        
        return true;
    }
    
    public static function validate_user_permissions( $capability = 'manage_options' ) {
        if ( ! current_user_can( $capability ) ) {
            wp_die( 'You do not have sufficient permissions to access this page.', 'Permission Error', [ 'response' => 403 ] );
        }
        
        return true;
    }
    
    public static function validate_api_key( $api_key, $provider ) {
        if ( empty( $api_key ) ) {
            return new WP_Error( 'empty_api_key', 'API key cannot be empty.' );
        }
        
        // Basic format validation
        switch ( $provider ) {
            case 'openai':
                if ( ! preg_match( '/^sk-[a-zA-Z0-9]{48}$/', $api_key ) ) {
                    return new WP_Error( 'invalid_openai_key', 'Invalid OpenAI API key format.' );
                }
                break;

            case 'gemini':
                if ( strlen( $api_key ) < 20 || ! preg_match( '/^[a-zA-Z0-9_-]+$/', $api_key ) ) {
                    return new WP_Error( 'invalid_gemini_key', 'Invalid Gemini API key format.' );
                }
                break;

            case 'openrouter':
                // OpenRouter keys can have various formats, be more flexible
                if ( strlen( $api_key ) < 10 || ! preg_match( '/^[a-zA-Z0-9_-]+$/', $api_key ) ) {
                    return new WP_Error( 'invalid_openrouter_key', 'Invalid OpenRouter API key format.' );
                }
                break;

            case 'pexels':
                if ( strlen( $api_key ) < 20 || ! preg_match( '/^[a-zA-Z0-9]+$/', $api_key ) ) {
                    return new WP_Error( 'invalid_pexels_key', 'Invalid Pexels API key format.' );
                }
                break;
        }
        
        return true;
    }
    
    public static function sanitize_generated_content( $content ) {
        // Remove potentially dangerous content
        $content = wp_kses_post( $content );
        
        // Remove script tags and event handlers
        $content = preg_replace( '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content );
        $content = preg_replace( '/\son\w+\s*=\s*["\'][^"\']*["\']/i', '', $content );
        
        // Sanitize URLs in content
        $content = preg_replace_callback(
            '/href\s*=\s*["\']([^"\']+)["\']/i',
            function( $matches ) {
                return 'href="' . esc_url( $matches[1] ) . '"';
            },
            $content
        );
        
        // Remove any remaining potentially dangerous attributes
        $content = preg_replace( '/\s(style|class)\s*=\s*["\'][^"\']*["\']/i', '', $content );
        
        return $content;
    }
    
    public static function validate_topic( $topic ) {
        if ( empty( $topic ) ) {
            return new WP_Error( 'empty_topic', 'Topic cannot be empty.' );
        }
        
        if ( strlen( $topic ) > 200 ) {
            return new WP_Error( 'topic_too_long', 'Topic is too long (max 200 characters).' );
        }
        
        // Check for potentially harmful content
        $dangerous_patterns = [
            '/\<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i'
        ];
        
        foreach ( $dangerous_patterns as $pattern ) {
            if ( preg_match( $pattern, $topic ) ) {
                return new WP_Error( 'dangerous_topic', 'Topic contains potentially dangerous content.' );
            }
        }
        
        return sanitize_text_field( $topic );
    }
    
    public static function validate_category( $category ) {
        $allowed_categories = AIBPG_Topic_Manager::get_available_categories();
        
        if ( ! in_array( $category, $allowed_categories ) ) {
            return new WP_Error( 'invalid_category', 'Invalid category selected.' );
        }
        
        return $category;
    }
    
    public static function rate_limit_check( $action, $limit = 10, $window = 3600 ) {
        $user_id = get_current_user_id();
        $ip_address = self::get_client_ip();
        $key = "aibpg_rate_limit_{$action}_{$user_id}_{$ip_address}";
        
        $attempts = get_transient( $key ) ?: 0;
        
        if ( $attempts >= $limit ) {
            return new WP_Error( 'rate_limit_exceeded', "Rate limit exceeded. Please wait before trying again." );
        }
        
        set_transient( $key, $attempts + 1, $window );
        
        return true;
    }
    
    public static function log_security_event( $event, $details = [] ) {
        $log_entry = [
            'timestamp' => current_time( 'mysql' ),
            'event' => $event,
            'user_id' => get_current_user_id(),
            'ip_address' => self::get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => $details
        ];
        
        $security_logs = get_option( 'aibpg_security_logs', [] );
        $security_logs[] = $log_entry;
        
        // Keep only last 100 security events
        if ( count( $security_logs ) > 100 ) {
            $security_logs = array_slice( $security_logs, -100 );
        }
        
        update_option( 'aibpg_security_logs', $security_logs );
        
        // Log critical events to error log
        if ( in_array( $event, [ 'unauthorized_access', 'invalid_nonce', 'rate_limit_exceeded' ] ) ) {
            error_log( "[AIBPG][SECURITY] {$event}: " . wp_json_encode( $log_entry ) );
        }
    }
    
    public static function encrypt_api_key( $api_key ) {
        if ( empty( $api_key ) ) {
            return '';
        }
        
        // Use WordPress salts for encryption
        $key = wp_salt( 'auth' );
        $iv = substr( wp_salt( 'secure_auth' ), 0, 16 );
        
        return base64_encode( openssl_encrypt( $api_key, 'AES-256-CBC', $key, 0, $iv ) );
    }
    
    public static function decrypt_api_key( $encrypted_key ) {
        if ( empty( $encrypted_key ) ) {
            return '';
        }
        
        $key = wp_salt( 'auth' );
        $iv = substr( wp_salt( 'secure_auth' ), 0, 16 );
        
        return openssl_decrypt( base64_decode( $encrypted_key ), 'AES-256-CBC', $key, 0, $iv );
    }
    
    public static function validate_settings( $settings ) {
        $errors = [];
        
        // Validate AI provider
        if ( ! in_array( $settings['ai_provider'] ?? '', [ 'openai', 'gemini', 'openrouter' ] ) ) {
            $errors[] = 'Invalid AI provider selected.';
        }
        
        // Validate API keys
        if ( $settings['ai_provider'] === 'openai' ) {
            $validation = self::validate_api_key( $settings['openai_api_key'] ?? '', 'openai' );
            if ( is_wp_error( $validation ) ) {
                $errors[] = $validation->get_error_message();
            }
        }
        
        if ( $settings['ai_provider'] === 'gemini' ) {
            $validation = self::validate_api_key( $settings['gemini_api_key'] ?? '', 'gemini' );
            if ( is_wp_error( $validation ) ) {
                $errors[] = $validation->get_error_message();
            }
        }

        if ( $settings['ai_provider'] === 'openrouter' ) {
            $validation = self::validate_api_key( $settings['openrouter_api_key'] ?? '', 'openrouter' );
            if ( is_wp_error( $validation ) ) {
                $errors[] = $validation->get_error_message();
            }
        }
        
        // Validate image provider
        if ( ! in_array( $settings['image_provider'] ?? '', [ 'pexels', 'none' ] ) ) {
            $errors[] = 'Invalid image provider selected.';
        }
        
        if ( $settings['image_provider'] === 'pexels' ) {
            $validation = self::validate_api_key( $settings['pexels_api_key'] ?? '', 'pexels' );
            if ( is_wp_error( $validation ) ) {
                $errors[] = $validation->get_error_message();
            }
        }
        
        // Validate categories
        if ( ! empty( $settings['categories'] ) ) {
            foreach ( $settings['categories'] as $category ) {
                $validation = self::validate_category( $category );
                if ( is_wp_error( $validation ) ) {
                    $errors[] = $validation->get_error_message();
                }
            }
        }
        
        return empty( $errors ) ? true : $errors;
    }
    
    private static function get_client_ip() {
        $ip_keys = [ 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR' ];
        
        foreach ( $ip_keys as $key ) {
            if ( ! empty( $_SERVER[ $key ] ) ) {
                $ip = $_SERVER[ $key ];
                if ( strpos( $ip, ',' ) !== false ) {
                    $ip = trim( explode( ',', $ip )[0] );
                }
                if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    public static function get_security_logs() {
        return get_option( 'aibpg_security_logs', [] );
    }
    
    public static function clear_security_logs() {
        return delete_option( 'aibpg_security_logs' );
    }
}
