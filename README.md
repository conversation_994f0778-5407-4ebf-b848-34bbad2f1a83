# AI Blog Post Generator

A comprehensive WordPress plugin that automatically generates high-quality blog posts using AI providers like OpenAI and Google Gemini, with advanced features including topic management, SEO optimization, image integration, and detailed analytics.

## Features

### 🤖 AI-Powered Content Generation
- **Multiple AI Providers**: Support for OpenAI (GPT-3.5) and Google Gemini
- **Advanced Content Templates**: Category-specific templates for tech, health, business, lifestyle, and education
- **SEO Optimization**: Built-in SEO best practices with meta descriptions and schema markup
- **Multi-language Support**: Generate content in English, Spanish, French, German, and Italian

### 📊 Smart Topic Management
- **Unique Topic Cycling**: Prevents duplicate content with intelligent topic rotation
- **Custom Topics**: Add your own topics for each category
- **5 Built-in Categories**: Tech, Health, Business, Lifestyle, Education with 15+ topics each
- **Random Topic Selection**: Ensures variety in content generation

### 🖼️ Image Integration
- **Pexels Integration**: Automatic featured image selection from Pexels
- **Image Cycling**: Prevents duplicate images with smart rotation
- **Optional Images**: Can be disabled if images aren't needed

### 📈 Analytics & Reporting
- **Comprehensive Dashboard**: Real-time statistics and performance metrics
- **Trending Topics**: Track which topics perform best
- **Category Performance**: Analyze success by content category
- **Export Analytics**: CSV and JSON export options
- **Success Rate Tracking**: Monitor generation success rates

### 🔒 Security & Reliability
- **Encrypted API Keys**: All API keys stored encrypted in database
- **Rate Limiting**: Prevents abuse with configurable rate limits
- **Input Validation**: Comprehensive validation and sanitization
- **Security Logging**: Track all security events
- **Error Handling**: Robust error handling with detailed logging

### ⚙️ Advanced Administration
- **Intuitive Dashboard**: Modern, tabbed interface with real-time data
- **Manual Generation**: Generate posts on-demand with one click
- **API Testing**: Test API connections before saving settings
- **Detailed Logs**: Separate logs for success, errors, and security events
- **Flexible Scheduling**: Daily or weekly automatic generation

## Installation

1. Upload the plugin files to `/wp-content/plugins/ai-blog-post-generator/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to 'AI Blog Post Generator' in the admin menu
4. Configure your API keys and settings

## Configuration

### Required API Keys

#### OpenAI (Recommended)
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Copy the key (starts with `sk-`)
4. Paste into the OpenAI API Key field

#### Google Gemini (Alternative)
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key
4. Paste into the Gemini API Key field

#### Pexels (Optional - for images)
1. Visit [Pexels API](https://www.pexels.com/api/)
2. Create a free account
3. Generate an API key
4. Paste into the Pexels API Key field

### Basic Settings

1. **AI Provider**: Choose between OpenAI or Gemini
2. **Image Provider**: Choose Pexels or None
3. **Categories**: Select which WordPress categories to use
4. **Languages**: Choose content languages
5. **Post Status**: Draft or Publish immediately
6. **Frequency**: Daily or Weekly generation

## Usage

### Automatic Generation
Once configured, the plugin will automatically generate posts based on your schedule:
- **Daily**: One post per day
- **Weekly**: One post per week

### Manual Generation
1. Go to AI Blog Post Generator → Generate Post
2. Click "Generate New Post"
3. Wait for the generation to complete
4. Review the generated post in your WordPress posts

### Monitoring
- **Dashboard**: View statistics and recent activity
- **Logs**: Monitor generation success and errors
- **Status**: Check configuration and system health
- **Security**: Review security events and logs

## Content Templates

The plugin uses sophisticated templates for each category:

### Tech Template
- Introduction with industry relevance
- Technical explanation (simplified)
- Current trends and statistics
- Benefits and applications
- Challenges and considerations
- Getting started guide
- Future outlook
- FAQ section

### Health Template
- Relatable health introduction
- Scientific foundation
- Evidence and research
- Practical benefits
- Implementation guide
- Safety considerations
- FAQ section

### Business Template
- Business challenge analysis
- Strategic framework
- Implementation strategy
- Case studies and examples
- Common pitfalls
- Success measurement
- FAQ section

### Lifestyle Template
- Personal connection
- Why it matters
- Getting started guide
- Detailed instructions
- Customization options
- Common challenges
- FAQ section

### Education Template
- Learning objectives
- Foundational concepts
- Detailed explanation
- Practical applications
- Learning resources
- Common misconceptions
- FAQ section

## Security Features

### Data Protection
- **Encrypted Storage**: API keys encrypted using WordPress salts
- **Secure Transmission**: All API calls use HTTPS
- **Input Sanitization**: All user inputs validated and sanitized
- **Content Filtering**: Generated content filtered for security

### Access Control
- **Permission Checks**: All actions require appropriate WordPress capabilities
- **Nonce Verification**: All forms protected with WordPress nonces
- **Rate Limiting**: Prevents abuse with configurable limits
- **Security Logging**: All security events logged and monitored

### Monitoring
- **Security Dashboard**: View all security events
- **Failed Attempts**: Track unauthorized access attempts
- **API Usage**: Monitor API key usage and potential abuse
- **Error Tracking**: Comprehensive error logging and reporting

## Troubleshooting

### Common Issues

#### "Class AIBPG_Main not found"
- **Solution**: Deactivate and reactivate the plugin
- **Cause**: Plugin files not properly loaded

#### "API connection failed"
- **Check**: API key format and validity
- **Test**: Use the "Test Connection" button in settings
- **Verify**: Account has sufficient API credits

#### "No topics available"
- **Solution**: Reset used topics or add custom topics
- **Check**: Selected categories have available topics
- **Add**: Custom topics through the topic manager

#### Posts not generating automatically
- **Check**: WordPress cron is working (`wp cron event list`)
- **Verify**: Plugin is properly scheduled
- **Test**: Manual generation works first

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## API Usage and Costs

### OpenAI Pricing (Approximate)
- **GPT-3.5-turbo**: ~$0.002 per 1K tokens
- **Average post**: ~1,500 tokens = $0.003 per post
- **Monthly cost**: ~$0.09 for daily posts

### Gemini Pricing
- **Free tier**: 60 requests per minute
- **Paid tier**: Starting at $0.001 per 1K characters

### Pexels
- **Free**: 200 requests per hour
- **No cost**: Completely free to use

## Hooks and Filters

### Actions
```php
// Fired after a post is generated
do_action('aibpg_post_generated', $post_id, $topic, $category, $meta, $image_url);
```

### Filters
```php
// Validate API keys
apply_filters('aibpg_validate_api_key', $api_key, $provider);

// Sanitize generated content
apply_filters('aibpg_sanitize_content', $content);
```

## Requirements

- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **Extensions**: cURL, OpenSSL
- **Permissions**: File write access for uploads
- **API Keys**: At least one AI provider key

## Support

For support, feature requests, or bug reports:
1. Check the troubleshooting section
2. Review the security and error logs
3. Test with manual generation first
4. Verify API key validity and credits

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release with full functionality
- OpenAI and Gemini support
- Advanced topic management
- Comprehensive analytics
- Security features
- Multi-language support
