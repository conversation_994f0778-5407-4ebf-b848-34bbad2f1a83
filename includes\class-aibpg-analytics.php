<?php
/**
 * Analytics and reporting for AI Blog Post Generator
 */
class AIBPG_Analytics {
    
    public static function init() {
        add_action( 'aibpg_post_generated', [ __CLASS__, 'track_post_generation' ], 10, 5 );
        add_action( 'wp_insert_post', [ __CLASS__, 'track_post_performance' ], 10, 2 );
    }
    
    public static function track_post_generation( $post_id, $topic, $category, $meta, $image_url ) {
        $analytics = get_option( 'aibpg_analytics', [] );
        
        $today = date( 'Y-m-d' );
        
        // Initialize today's data if not exists
        if ( ! isset( $analytics[ $today ] ) ) {
            $analytics[ $today ] = [
                'posts_generated' => 0,
                'categories' => [],
                'ai_provider' => '',
                'success_rate' => 100,
                'topics' => []
            ];
        }
        
        // Update analytics
        $analytics[ $today ]['posts_generated']++;
        $analytics[ $today ]['categories'][ $category ] = ( $analytics[ $today ]['categories'][ $category ] ?? 0 ) + 1;
        $analytics[ $today ]['ai_provider'] = AIBPG_Settings::get( 'ai_provider' );
        $analytics[ $today ]['topics'][] = $topic;
        
        // Keep only last 30 days
        $analytics = array_slice( $analytics, -30, null, true );
        
        update_option( 'aibpg_analytics', $analytics );
    }
    
    public static function track_post_performance( $post_id, $post ) {
        // Only track our generated posts
        if ( ! get_post_meta( $post_id, '_aibpg_generated', true ) ) {
            return;
        }
        
        // Track post views (simple implementation)
        $views = get_post_meta( $post_id, '_aibpg_views', true ) ?: 0;
        update_post_meta( $post_id, '_aibpg_views', $views );
    }
    
    public static function get_dashboard_stats() {
        $analytics = get_option( 'aibpg_analytics', [] );
        $generated_posts = get_posts([
            'meta_key' => '_aibpg_generated',
            'meta_value' => true,
            'numberposts' => -1,
            'post_status' => 'any'
        ]);
        
        $stats = [
            'total_posts' => count( $generated_posts ),
            'posts_this_month' => 0,
            'posts_this_week' => 0,
            'most_used_category' => '',
            'success_rate' => 0,
            'avg_posts_per_day' => 0,
            'category_breakdown' => [],
            'recent_activity' => []
        ];
        
        // Calculate monthly and weekly stats
        $this_month = date( 'Y-m' );
        $this_week_start = date( 'Y-m-d', strtotime( 'monday this week' ) );
        
        foreach ( $generated_posts as $post ) {
            $post_date = date( 'Y-m-d', strtotime( $post->post_date ) );
            $post_month = date( 'Y-m', strtotime( $post->post_date ) );
            
            if ( $post_month === $this_month ) {
                $stats['posts_this_month']++;
            }
            
            if ( $post_date >= $this_week_start ) {
                $stats['posts_this_week']++;
            }
            
            // Category breakdown
            $category = get_post_meta( $post->ID, '_aibpg_category', true );
            if ( $category ) {
                $stats['category_breakdown'][ $category ] = ( $stats['category_breakdown'][ $category ] ?? 0 ) + 1;
            }
        }
        
        // Find most used category
        if ( ! empty( $stats['category_breakdown'] ) ) {
            $stats['most_used_category'] = array_keys( $stats['category_breakdown'], max( $stats['category_breakdown'] ) )[0];
        }
        
        // Calculate success rate from logs
        $success_logs = get_option( 'aibpg_success_logs', [] );
        $error_logs = get_option( 'aibpg_error_logs', [] );
        $total_attempts = count( $success_logs ) + count( $error_logs );
        
        if ( $total_attempts > 0 ) {
            $stats['success_rate'] = round( ( count( $success_logs ) / $total_attempts ) * 100 );
        }
        
        // Calculate average posts per day (last 30 days)
        if ( ! empty( $analytics ) ) {
            $total_days = count( $analytics );
            $total_posts_in_period = array_sum( array_column( $analytics, 'posts_generated' ) );
            $stats['avg_posts_per_day'] = $total_days > 0 ? round( $total_posts_in_period / $total_days, 1 ) : 0;
        }
        
        // Recent activity (last 7 days)
        $stats['recent_activity'] = array_slice( $analytics, -7, null, true );
        
        return $stats;
    }
    
    public static function get_performance_data() {
        $generated_posts = get_posts([
            'meta_key' => '_aibpg_generated',
            'meta_value' => true,
            'numberposts' => 50,
            'post_status' => 'publish'
        ]);
        
        $performance = [];
        
        foreach ( $generated_posts as $post ) {
            $views = get_post_meta( $post->ID, '_aibpg_views', true ) ?: 0;
            $comments = wp_count_comments( $post->ID );
            $topic = get_post_meta( $post->ID, '_aibpg_topic', true );
            $category = get_post_meta( $post->ID, '_aibpg_category', true );
            
            $performance[] = [
                'id' => $post->ID,
                'title' => $post->post_title,
                'topic' => $topic,
                'category' => $category,
                'views' => $views,
                'comments' => $comments->approved,
                'date' => $post->post_date,
                'status' => $post->post_status,
                'word_count' => str_word_count( strip_tags( $post->post_content ) )
            ];
        }
        
        return $performance;
    }
    
    public static function get_category_performance() {
        $generated_posts = get_posts([
            'meta_key' => '_aibpg_generated',
            'meta_value' => true,
            'numberposts' => -1,
            'post_status' => 'publish'
        ]);
        
        $category_stats = [];
        
        foreach ( $generated_posts as $post ) {
            $category = get_post_meta( $post->ID, '_aibpg_category', true ) ?: 'uncategorized';
            $views = get_post_meta( $post->ID, '_aibpg_views', true ) ?: 0;
            $comments = wp_count_comments( $post->ID );
            
            if ( ! isset( $category_stats[ $category ] ) ) {
                $category_stats[ $category ] = [
                    'posts' => 0,
                    'total_views' => 0,
                    'total_comments' => 0,
                    'avg_views' => 0,
                    'avg_comments' => 0
                ];
            }
            
            $category_stats[ $category ]['posts']++;
            $category_stats[ $category ]['total_views'] += $views;
            $category_stats[ $category ]['total_comments'] += $comments->approved;
        }
        
        // Calculate averages
        foreach ( $category_stats as $category => &$stats ) {
            $stats['avg_views'] = $stats['posts'] > 0 ? round( $stats['total_views'] / $stats['posts'] ) : 0;
            $stats['avg_comments'] = $stats['posts'] > 0 ? round( $stats['total_comments'] / $stats['posts'], 1 ) : 0;
        }
        
        return $category_stats;
    }
    
    public static function get_trending_topics() {
        $generated_posts = get_posts([
            'meta_key' => '_aibpg_generated',
            'meta_value' => true,
            'numberposts' => 100,
            'post_status' => 'publish',
            'date_query' => [
                'after' => '30 days ago'
            ]
        ]);
        
        $topic_performance = [];
        
        foreach ( $generated_posts as $post ) {
            $topic = get_post_meta( $post->ID, '_aibpg_topic', true );
            $views = get_post_meta( $post->ID, '_aibpg_views', true ) ?: 0;
            $comments = wp_count_comments( $post->ID );
            
            if ( $topic ) {
                if ( ! isset( $topic_performance[ $topic ] ) ) {
                    $topic_performance[ $topic ] = [
                        'views' => 0,
                        'comments' => 0,
                        'posts' => 0,
                        'score' => 0
                    ];
                }
                
                $topic_performance[ $topic ]['views'] += $views;
                $topic_performance[ $topic ]['comments'] += $comments->approved;
                $topic_performance[ $topic ]['posts']++;
                
                // Calculate engagement score (views + comments * 10)
                $topic_performance[ $topic ]['score'] = $topic_performance[ $topic ]['views'] + ( $topic_performance[ $topic ]['comments'] * 10 );
            }
        }
        
        // Sort by score
        uasort( $topic_performance, function( $a, $b ) {
            return $b['score'] - $a['score'];
        });
        
        return array_slice( $topic_performance, 0, 10, true );
    }
    
    public static function export_analytics( $format = 'csv' ) {
        $stats = self::get_dashboard_stats();
        $performance = self::get_performance_data();
        
        if ( $format === 'csv' ) {
            return self::export_to_csv( $performance );
        }
        
        return self::export_to_json( $stats, $performance );
    }
    
    private static function export_to_csv( $data ) {
        $csv = "Title,Topic,Category,Views,Comments,Date,Status,Word Count\n";
        
        foreach ( $data as $row ) {
            $csv .= sprintf(
                '"%s","%s","%s",%d,%d,"%s","%s",%d' . "\n",
                $row['title'],
                $row['topic'],
                $row['category'],
                $row['views'],
                $row['comments'],
                $row['date'],
                $row['status'],
                $row['word_count']
            );
        }
        
        return $csv;
    }
    
    private static function export_to_json( $stats, $performance ) {
        return wp_json_encode([
            'stats' => $stats,
            'performance' => $performance,
            'exported_at' => current_time( 'mysql' )
        ], JSON_PRETTY_PRINT );
    }
}
