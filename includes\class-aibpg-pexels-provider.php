<?php
/**
 * Pexels provider implementation
 */
class AIBPG_Pexels_Provider implements AIBPG_Image_Provider_Interface {
    private $api_key;
    private $endpoint = 'https://api.pexels.com/v1/search';
    private $option_key = 'aibpg_pexels_used_images';

    public function __construct() {
        $this->api_key = AIBPG_Settings::get('pexels_api_key');
    }

    public function get_name() {
        return 'pexels';
    }

    public function fetch_image( $keywords, $options = [] ) {
        $used = $this->get_used_images($keywords);
        $images = $this->search_images($keywords);
        if (empty($images)) return '';
        $unused = array_diff($images, $used);
        if (empty($unused)) {
            $this->reset_used_images($keywords);
            $unused = $images;
        }
        $image = array_shift($unused);
        $this->mark_image_used($keywords, $image);
        return $image;
    }

    private function search_images($keywords) {
        $args = [
            'headers' => [
                'Authorization' => $this->api_key,
            ],
            'timeout' => 20,
        ];
        $url = $this->endpoint . '?query=' . urlencode($keywords) . '&per_page=15';
        $retries = 2;
        while ($retries >= 0) {
            $response = wp_remote_get($url, $args);
            if (is_wp_error($response)) {
                $retries--;
                sleep(1);
                continue;
            }
            $data = json_decode(wp_remote_retrieve_body($response), true);
            if (!empty($data['photos'])) {
                $urls = array_map(function($photo) {
                    return $photo['src']['large'] ?? '';
                }, $data['photos']);
                return array_filter($urls);
            }
            $retries--;
            sleep(1);
        }
        error_log('[AIBPG][Pexels] API call failed for: ' . $keywords);
        return [];
    }

    private function get_used_images($keywords) {
        $all = get_option($this->option_key, []);
        return $all[$keywords] ?? [];
    }
    private function mark_image_used($keywords, $image) {
        $all = get_option($this->option_key, []);
        if (!isset($all[$keywords])) $all[$keywords] = [];
        if (!in_array($image, $all[$keywords])) {
            $all[$keywords][] = $image;
            update_option($this->option_key, $all);
        }
    }
    private function reset_used_images($keywords) {
        $all = get_option($this->option_key, []);
        $all[$keywords] = [];
        update_option($this->option_key, $all);
    }
} 