<?php
/**
 * Test configuration status display fix
 * Run this to verify the status checking works correctly
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>Testing Configuration Status Fix</h2>";

// Get current settings
$settings = AIBPG_Settings::all();

echo "<h3>Current Settings:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";

echo "<tr><td><strong>AI Provider</strong></td><td>" . ($settings['ai_provider'] ?? 'Not set') . "</td></tr>";
echo "<tr><td><strong>OpenAI API Key</strong></td><td>" . (empty($settings['openai_api_key']) ? 'Not set' : 'Set (' . strlen($settings['openai_api_key']) . ' chars)') . "</td></tr>";
echo "<tr><td><strong>Gemini API Key</strong></td><td>" . (empty($settings['gemini_api_key']) ? 'Not set' : 'Set (' . strlen($settings['gemini_api_key']) . ' chars)') . "</td></tr>";
echo "<tr><td><strong>OpenRouter API Key</strong></td><td>" . (empty($settings['openrouter_api_key']) ? 'Not set' : 'Set (' . strlen($settings['openrouter_api_key']) . ' chars)') . "</td></tr>";
echo "<tr><td><strong>OpenRouter Model</strong></td><td>" . ($settings['openrouter_model'] ?? 'Not set') . "</td></tr>";
echo "<tr><td><strong>Image Provider</strong></td><td>" . ($settings['image_provider'] ?? 'Not set') . "</td></tr>";
echo "<tr><td><strong>Pexels API Key</strong></td><td>" . (empty($settings['pexels_api_key']) ? 'Not set' : 'Set (' . strlen($settings['pexels_api_key']) . ' chars)') . "</td></tr>";

echo "</table>";

// Test the check_api_status method
echo "<h3>API Status Check Results:</h3>";

if ( class_exists( 'AIBPG_Admin' ) && method_exists( 'AIBPG_Admin', 'check_api_status' ) ) {
    // Use reflection to access the private method
    $reflection = new ReflectionClass( 'AIBPG_Admin' );
    $method = $reflection->getMethod( 'check_api_status' );
    $method->setAccessible( true );
    
    $api_status = $method->invoke( null, $settings );
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Check</th><th>Result</th><th>Status</th></tr>";
    
    $ai_status = $api_status['ai_configured'] ? '✅ Configured' : '❌ Not configured';
    echo "<tr><td><strong>AI Provider</strong></td><td>" . ($api_status['ai_configured'] ? 'true' : 'false') . "</td><td>{$ai_status}</td></tr>";
    
    $image_status = $api_status['image_configured'] ? '✅ Configured' : '❌ Not configured';
    echo "<tr><td><strong>Image Provider</strong></td><td>" . ($api_status['image_configured'] ? 'true' : 'false') . "</td><td>{$image_status}</td></tr>";
    
    echo "</table>";
    
    // Detailed AI provider check
    echo "<h4>AI Provider Detailed Check:</h4>";
    echo "<ul>";
    
    if ( $settings['ai_provider'] === 'openai' ) {
        $openai_configured = !empty( $settings['openai_api_key'] );
        echo "<li><strong>OpenAI:</strong> " . ($openai_configured ? '✅ API key present' : '❌ API key missing') . "</li>";
    }
    
    if ( $settings['ai_provider'] === 'gemini' ) {
        $gemini_configured = !empty( $settings['gemini_api_key'] );
        echo "<li><strong>Gemini:</strong> " . ($gemini_configured ? '✅ API key present' : '❌ API key missing') . "</li>";
    }
    
    if ( $settings['ai_provider'] === 'openrouter' ) {
        $openrouter_configured = !empty( $settings['openrouter_api_key'] );
        echo "<li><strong>OpenRouter:</strong> " . ($openrouter_configured ? '✅ API key present' : '❌ API key missing') . "</li>";
        if ( $openrouter_configured ) {
            echo "<li><strong>OpenRouter Model:</strong> " . ($settings['openrouter_model'] ?? 'mistralai/mistral-7b-instruct:free') . "</li>";
        }
    }
    
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>❌ AIBPG_Admin class or check_api_status method not available</p>";
}

// Test connection if configured
echo "<h3>Connection Test:</h3>";

if ( $api_status['ai_configured'] ?? false ) {
    echo "<p>Testing connection to configured AI provider...</p>";
    
    try {
        switch ( $settings['ai_provider'] ) {
            case 'openai':
                if ( class_exists( 'AIBPG_OpenAI_Provider' ) ) {
                    $provider = new AIBPG_OpenAI_Provider();
                    $test_result = $provider->test_connection();
                    $status = $test_result['success'] ? '✅ Success' : '❌ Failed';
                    echo "<p><strong>OpenAI:</strong> {$status} - {$test_result['message']}</p>";
                } else {
                    echo "<p><strong>OpenAI:</strong> ❌ Provider class not available</p>";
                }
                break;
                
            case 'gemini':
                if ( class_exists( 'AIBPG_Gemini_Provider' ) ) {
                    $provider = new AIBPG_Gemini_Provider();
                    $test_result = $provider->test_connection();
                    $status = $test_result['success'] ? '✅ Success' : '❌ Failed';
                    echo "<p><strong>Gemini:</strong> {$status} - {$test_result['message']}</p>";
                } else {
                    echo "<p><strong>Gemini:</strong> ❌ Provider class not available</p>";
                }
                break;
                
            case 'openrouter':
                if ( class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
                    $provider = new AIBPG_OpenRouter_Provider();
                    $test_result = $provider->test_connection();
                    $status = $test_result['success'] ? '✅ Success' : '❌ Failed';
                    echo "<p><strong>OpenRouter:</strong> {$status} - {$test_result['message']}</p>";
                    
                    if ( isset( $test_result['debug'] ) ) {
                        echo "<details><summary>Debug Information</summary>";
                        echo "<pre>" . esc_html( print_r( $test_result['debug'], true ) ) . "</pre>";
                        echo "</details>";
                    }
                } else {
                    echo "<p><strong>OpenRouter:</strong> ❌ Provider class not available</p>";
                }
                break;
                
            default:
                echo "<p>❌ Unknown AI provider: " . $settings['ai_provider'] . "</p>";
        }
        
    } catch ( Exception $e ) {
        echo "<p style='color: red;'>❌ Error during connection test: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: orange;'>⚠️ AI provider not configured - cannot test connection</p>";
}

// Test scheduler status
echo "<h3>Scheduler Status:</h3>";

if ( class_exists( 'AIBPG_Scheduler' ) ) {
    $next_scheduled = AIBPG_Scheduler::get_next_scheduled();
    
    if ( $next_scheduled ) {
        echo "<p>✅ <strong>Scheduled:</strong> " . date( 'Y-m-d H:i:s', $next_scheduled ) . "</p>";
        echo "<p><strong>Frequency:</strong> " . ($settings['post_frequency'] ?? 'daily') . "</p>";
    } else {
        echo "<p>❌ <strong>Not scheduled</strong></p>";
        echo "<p>Attempting to schedule...</p>";
        
        AIBPG_Scheduler::schedule_next_event();
        $next_scheduled = AIBPG_Scheduler::get_next_scheduled();
        
        if ( $next_scheduled ) {
            echo "<p>✅ <strong>Scheduled successfully:</strong> " . date( 'Y-m-d H:i:s', $next_scheduled ) . "</p>";
        } else {
            echo "<p>❌ <strong>Failed to schedule</strong></p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ AIBPG_Scheduler class not available</p>";
}

echo "<h3>Status Page Preview:</h3>";
echo "<div style='border: 1px solid #ddd; padding: 15px; background: #f9f9f9;'>";
echo "<h4>Configuration Status</h4>";

// Simulate the status page display
$ai_provider_display = ucfirst( $settings['ai_provider'] ?? 'None' );
$ai_status_display = ($api_status['ai_configured'] ?? false) ? '<span style="color: green;">✓ Configured</span>' : '<span style="color: red;">✗ Not configured</span>';

echo "<p><strong>AI Provider:</strong> {$ai_provider_display} {$ai_status_display}</p>";

if ( $settings['ai_provider'] === 'openrouter' && ($api_status['ai_configured'] ?? false) ) {
    $model = $settings['openrouter_model'] ?? 'mistralai/mistral-7b-instruct:free';
    echo "<p><small>Model: {$model}</small></p>";
}

$image_provider_display = ucfirst( $settings['image_provider'] ?? 'None' );
$image_status_display = ($api_status['image_configured'] ?? false) ? '<span style="color: green;">✓ Configured</span>' : '<span style="color: red;">✗ Not configured</span>';

echo "<p><strong>Image Provider:</strong> {$image_provider_display} {$image_status_display}</p>";

$frequency_display = ucfirst( $settings['post_frequency'] ?? 'Daily' );
echo "<p><strong>Scheduling:</strong> {$frequency_display}</p>";

if ( $next_scheduled ) {
    echo "<p><small>Next: " . date( 'Y-m-d H:i:s', $next_scheduled ) . "</small></p>";
}

echo "</div>";

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>OpenRouter Status Check:</strong> " . (($settings['ai_provider'] === 'openrouter' && ($api_status['ai_configured'] ?? false)) ? '✅ Working' : '❌ Issue found') . "</li>";
echo "<li><strong>Configuration Display:</strong> " . (isset($api_status) ? '✅ Working' : '❌ Issue found') . "</li>";
echo "<li><strong>Connection Testing:</strong> " . (class_exists('AIBPG_OpenRouter_Provider') ? '✅ Available' : '❌ Missing') . "</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>If OpenRouter shows ✅ Configured, the fix is working</li>";
echo "<li>Go to AI Blog Post Generator → Status to see the updated display</li>";
echo "<li>Try the 'Test Connection' button on the status page</li>";
echo "<li>The status should now correctly show OpenRouter as configured</li>";
echo "</ul>";
?>
