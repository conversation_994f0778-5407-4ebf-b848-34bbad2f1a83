<?php
/**
 * Main plugin class for AI Blog Post Generator
 */
class AIBPG_Main {
    public static function init() {
        self::includes();
        AIBPG_Security::init();
        AIBPG_Settings::init();
        AIBPG_Admin::init();
        AIBPG_Scheduler::init();
        AIBPG_Analytics::init();
        AIBPG_Generator::init();
    }

    private static function includes() {
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-security.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-settings.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-admin.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-scheduler.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-content-templates.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-content-processor.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-analytics.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-generator.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-topic-manager.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-ai-provider-interface.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-openai-provider.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-gemini-provider.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-openrouter-provider.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-image-provider-interface.php';
        require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-pexels-provider.php';

        // Load tests in development/debug mode
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG && file_exists( AIBPG_PLUGIN_DIR . 'tests/test-plugin-functionality.php' ) ) {
            require_once AIBPG_PLUGIN_DIR . 'tests/test-plugin-functionality.php';
        }
    }

    // Activation/deactivation now handled in main plugin file
    // to ensure proper class loading order
}

