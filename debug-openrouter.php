<?php
/**
 * Debug OpenRouter API connection issues
 * Run this to diagnose OpenRouter connection problems
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>OpenRouter API Connection Debug</h2>";

// Get current settings
$settings = AIBPG_Settings::all();
$api_key = $settings['openrouter_api_key'] ?? '';
$model = $settings['openrouter_model'] ?? 'mistralai/mistral-7b-instruct:free';

echo "<h3>Current Configuration:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

$api_key_display = empty( $api_key ) ? 'Not set' : substr( $api_key, 0, 10 ) . '...';
$api_key_status = empty( $api_key ) ? '❌ Missing' : '✅ Set';
echo "<tr><td><strong>API Key</strong></td><td>{$api_key_display}</td><td>{$api_key_status}</td></tr>";

echo "<tr><td><strong>Model</strong></td><td>{$model}</td><td>ℹ️ Info</td></tr>";

$provider_status = class_exists( 'AIBPG_OpenRouter_Provider' ) ? '✅ Available' : '❌ Missing';
echo "<tr><td><strong>Provider Class</strong></td><td>AIBPG_OpenRouter_Provider</td><td>{$provider_status}</td></tr>";

echo "</table>";

if ( empty( $api_key ) ) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h4>❌ API Key Missing</h4>";
    echo "<p>Please configure your OpenRouter API key in the plugin settings first.</p>";
    echo "<p><strong>Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Go to <a href='https://openrouter.ai/keys' target='_blank'>OpenRouter API Keys</a></li>";
    echo "<li>Create a new API key</li>";
    echo "<li>Copy the key and paste it in AI Blog Post Generator → Settings</li>";
    echo "</ol>";
    echo "</div>";
    exit;
}

// Test API key format
echo "<h3>API Key Validation:</h3>";
$key_length = strlen( $api_key );
$key_format = preg_match( '/^[a-zA-Z0-9_-]+$/', $api_key );

echo "<ul>";
echo "<li><strong>Length:</strong> {$key_length} characters " . ( $key_length >= 10 ? '✅' : '❌' ) . "</li>";
echo "<li><strong>Format:</strong> " . ( $key_format ? '✅ Valid characters' : '❌ Invalid characters' ) . "</li>";
echo "</ul>";

// Test available models
echo "<h3>Available Models:</h3>";
if ( class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    $available_models = AIBPG_OpenRouter_Provider::get_available_models();
    $free_models = AIBPG_OpenRouter_Provider::get_free_models();
    
    echo "<p><strong>Total Models:</strong> " . count( $available_models ) . "</p>";
    echo "<p><strong>Free Models:</strong> " . count( $free_models ) . "</p>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Model ID</th><th>Name</th><th>Free</th><th>Max Tokens</th></tr>";
    
    foreach ( $available_models as $model_id => $model_info ) {
        $free_status = $model_info['free'] ? '✅ Yes' : '❌ No';
        $current = $model_id === $model ? ' (CURRENT)' : '';
        echo "<tr>";
        echo "<td><strong>{$model_id}{$current}</strong></td>";
        echo "<td>{$model_info['name']}</td>";
        echo "<td>{$free_status}</td>";
        echo "<td>{$model_info['max_tokens']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ AIBPG_OpenRouter_Provider class not available</p>";
}

// Test basic HTTP connectivity
echo "<h3>HTTP Connectivity Test:</h3>";
$test_url = 'https://openrouter.ai/api/v1/models';
$http_response = wp_remote_get( $test_url, [ 'timeout' => 10 ] );

if ( is_wp_error( $http_response ) ) {
    echo "<p>❌ <strong>HTTP Error:</strong> " . $http_response->get_error_message() . "</p>";
} else {
    $response_code = wp_remote_retrieve_response_code( $http_response );
    echo "<p>✅ <strong>HTTP Response:</strong> {$response_code}</p>";
    
    if ( $response_code === 200 ) {
        echo "<p>✅ OpenRouter API endpoint is reachable</p>";
    } else {
        echo "<p>⚠️ Unexpected response code: {$response_code}</p>";
    }
}

// Test API authentication
echo "<h3>API Authentication Test:</h3>";
$auth_test_url = 'https://openrouter.ai/api/v1/models';
$auth_response = wp_remote_get( $auth_test_url, [
    'timeout' => 10,
    'headers' => [
        'Authorization' => 'Bearer ' . $api_key,
        'HTTP-Referer' => home_url(),
        'X-Title' => 'AI Blog Post Generator - Debug Test'
    ]
]);

if ( is_wp_error( $auth_response ) ) {
    echo "<p>❌ <strong>Auth Test Error:</strong> " . $auth_response->get_error_message() . "</p>";
} else {
    $auth_code = wp_remote_retrieve_response_code( $auth_response );
    echo "<p><strong>Auth Response Code:</strong> {$auth_code}</p>";
    
    if ( $auth_code === 200 ) {
        echo "<p>✅ API key authentication successful</p>";
    } elseif ( $auth_code === 401 ) {
        echo "<p>❌ <strong>Authentication failed:</strong> Invalid API key</p>";
    } elseif ( $auth_code === 403 ) {
        echo "<p>❌ <strong>Access forbidden:</strong> API key may not have required permissions</p>";
    } else {
        echo "<p>⚠️ <strong>Unexpected auth response:</strong> {$auth_code}</p>";
        $auth_body = wp_remote_retrieve_body( $auth_response );
        echo "<pre>" . esc_html( substr( $auth_body, 0, 500 ) ) . "</pre>";
    }
}

// Test actual API call
echo "<h3>API Call Test:</h3>";
if ( class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    $provider = new AIBPG_OpenRouter_Provider();
    $test_result = $provider->test_connection();
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Property</th><th>Value</th></tr>";
    echo "<tr><td><strong>Success</strong></td><td>" . ( $test_result['success'] ? '✅ Yes' : '❌ No' ) . "</td></tr>";
    echo "<tr><td><strong>Message</strong></td><td>" . esc_html( $test_result['message'] ) . "</td></tr>";
    echo "<tr><td><strong>Model</strong></td><td>" . esc_html( $test_result['model'] ) . "</td></tr>";
    
    if ( isset( $test_result['response_preview'] ) ) {
        echo "<tr><td><strong>Response Preview</strong></td><td>" . esc_html( $test_result['response_preview'] ) . "</td></tr>";
    }
    
    if ( isset( $test_result['debug'] ) ) {
        echo "<tr><td><strong>Debug Info</strong></td><td><pre>" . esc_html( print_r( $test_result['debug'], true ) ) . "</pre></td></tr>";
    }
    
    echo "</table>";
    
    if ( $test_result['success'] ) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>✅ Connection Successful!</h4>";
        echo "<p>Your OpenRouter API key is working correctly with model: <strong>{$test_result['model']}</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>❌ Connection Failed</h4>";
        echo "<p><strong>Error:</strong> {$test_result['message']}</p>";
        
        if ( isset( $test_result['debug'] ) ) {
            $debug = $test_result['debug'];
            
            if ( isset( $debug['response_code'] ) ) {
                echo "<p><strong>HTTP Code:</strong> {$debug['response_code']}</p>";
            }
            
            if ( isset( $debug['api_error'] ) ) {
                $api_error = $debug['api_error'];
                echo "<p><strong>API Error:</strong> " . ( $api_error['message'] ?? 'Unknown' ) . "</p>";
                if ( isset( $api_error['code'] ) ) {
                    echo "<p><strong>Error Code:</strong> {$api_error['code']}</p>";
                }
            }
        }
        echo "</div>";
    }
} else {
    echo "<p>❌ Cannot test - AIBPG_OpenRouter_Provider class not available</p>";
}

// Troubleshooting suggestions
echo "<h3>Troubleshooting Suggestions:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h4>If connection is failing:</h4>";
echo "<ol>";
echo "<li><strong>Verify API Key:</strong> Make sure you copied the complete API key from OpenRouter</li>";
echo "<li><strong>Check Credits:</strong> Visit <a href='https://openrouter.ai/activity' target='_blank'>OpenRouter Activity</a> to check your usage and credits</li>";
echo "<li><strong>Try Different Model:</strong> Some models may be temporarily unavailable</li>";
echo "<li><strong>Check Network:</strong> Ensure your server can make outbound HTTPS requests</li>";
echo "<li><strong>API Key Permissions:</strong> Make sure your API key has the necessary permissions</li>";
echo "</ol>";

echo "<h4>Common Issues:</h4>";
echo "<ul>";
echo "<li><strong>401 Unauthorized:</strong> Invalid or expired API key</li>";
echo "<li><strong>429 Rate Limited:</strong> Too many requests, wait and try again</li>";
echo "<li><strong>400 Bad Request:</strong> Model not available or invalid parameters</li>";
echo "<li><strong>HTTP Timeout:</strong> Network connectivity issues</li>";
echo "</ul>";
echo "</div>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>If the test shows ✅ success, your OpenRouter integration is working</li>";
echo "<li>If it shows ❌ failure, check the error details above</li>";
echo "<li>Try the 'Test Connection' button in the plugin settings</li>";
echo "<li>Generate a test post to verify full functionality</li>";
echo "</ul>";
?>
