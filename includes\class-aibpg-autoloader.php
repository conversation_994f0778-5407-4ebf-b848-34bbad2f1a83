<?php
/**
 * Autoloader for AI Blog Post Generator plugin classes
 */
class AIBPG_Autoloader {
    public static function register() {
        spl_autoload_register( [ __CLASS__, 'autoload' ] );
    }

    public static function autoload( $class ) {
        if ( strpos( $class, 'AIBPG_' ) !== 0 ) {
            return;
        }
        $file = AIBPG_PLUGIN_DIR . 'includes/' . strtolower( str_replace( '_', '-', $class ) ) . '.php';
        if ( file_exists( $file ) ) {
            require_once $file;
        }
    }
}

AIBPG_Autoloader::register();