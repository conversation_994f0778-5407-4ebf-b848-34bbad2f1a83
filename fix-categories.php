<?php
/**
 * One-time script to fix invalid categories in settings
 * Run this once to clean up any invalid categories like "entertainment-news"
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>Fixing Invalid Categories</h2>";

// Get current settings
$current_settings = AIBPG_Settings::all();
$available_categories = AIBPG_Topic_Manager::get_available_categories();

echo "<h3>Current Settings:</h3>";
echo "<p><strong>AI Provider:</strong> " . ($current_settings['ai_provider'] ?? 'Not set') . "</p>";
echo "<p><strong>Current Categories:</strong> " . implode(', ', $current_settings['categories'] ?? []) . "</p>";
echo "<p><strong>Available Categories:</strong> " . implode(', ', $available_categories) . "</p>";

// Check for invalid categories
$invalid_categories = [];
$valid_categories = [];

foreach ( $current_settings['categories'] ?? [] as $category ) {
    if ( in_array( $category, $available_categories ) ) {
        $valid_categories[] = $category;
    } else {
        $invalid_categories[] = $category;
        
        // Try to map to valid category
        $mapped_category = map_invalid_category( $category, $available_categories );
        if ( $mapped_category && !in_array( $mapped_category, $valid_categories ) ) {
            $valid_categories[] = $mapped_category;
            echo "<p>✅ Mapped '{$category}' → '{$mapped_category}'</p>";
        } else {
            echo "<p>❌ Could not map '{$category}' to valid category</p>";
        }
    }
}

if ( !empty( $invalid_categories ) ) {
    echo "<h3>Fixing Categories:</h3>";
    
    // If no valid categories remain, add default
    if ( empty( $valid_categories ) ) {
        $valid_categories = ['tech'];
        echo "<p>⚠️ No valid categories found, using default: tech</p>";
    }
    
    // Update settings
    AIBPG_Settings::update( 'categories', $valid_categories );
    
    echo "<p>✅ Updated categories to: " . implode(', ', $valid_categories) . "</p>";
    
    // Verify the fix
    $updated_settings = AIBPG_Settings::all();
    echo "<p><strong>New Categories:</strong> " . implode(', ', $updated_settings['categories']) . "</p>";
    
} else {
    echo "<p>✅ All categories are valid, no changes needed.</p>";
}

// Test topic generation for each category
echo "<h3>Testing Topic Generation:</h3>";
foreach ( $valid_categories as $category ) {
    $topic = AIBPG_Topic_Manager::get_next_topic( $category );
    if ( $topic ) {
        echo "<p>✅ <strong>{$category}:</strong> {$topic}</p>";
    } else {
        echo "<p>❌ <strong>{$category}:</strong> No topics available</p>";
    }
}

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li>Available categories: " . count($available_categories) . "</li>";
echo "<li>Valid categories in settings: " . count($valid_categories) . "</li>";
echo "<li>Invalid categories removed: " . count($invalid_categories) . "</li>";
echo "</ul>";

if ( !empty( $invalid_categories ) ) {
    echo "<p><strong>Removed invalid categories:</strong> " . implode(', ', $invalid_categories) . "</p>";
}

echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>Try generating a post manually to test the fix</li>";
echo "<li>Check that no more 'Invalid category' errors appear in logs</li>";
echo "<li>The plugin should now work with valid categories</li>";
echo "</ul>";

function map_invalid_category( $invalid_category, $available_categories ) {
    $mappings = [
        'entertainment-news' => 'entertainment',
        'entertainment_news' => 'entertainment',
        'news' => 'entertainment',
        'movies' => 'entertainment',
        'tv' => 'entertainment',
        'music' => 'entertainment',
        'gaming' => 'entertainment',
        'celebrity' => 'entertainment',
        'tech-news' => 'tech',
        'technology' => 'tech',
        'science' => 'tech',
        'health-news' => 'health',
        'wellness' => 'health',
        'fitness' => 'health',
        'business-news' => 'business',
        'finance' => 'business',
        'marketing' => 'business',
        'startup' => 'business',
        'life' => 'lifestyle',
        'travel' => 'lifestyle',
        'food' => 'lifestyle',
        'fashion' => 'lifestyle',
        'learning' => 'education',
        'school' => 'education',
        'university' => 'education',
        'training' => 'education'
    ];
    
    // Direct mapping
    if ( isset( $mappings[ $invalid_category ] ) ) {
        return $mappings[ $invalid_category ];
    }
    
    // Partial match
    foreach ( $available_categories as $available_cat ) {
        if ( strpos( $invalid_category, $available_cat ) !== false || strpos( $available_cat, $invalid_category ) !== false ) {
            return $available_cat;
        }
    }
    
    return null;
}
?>
