<?php
/**
 * Plugin Name: AutoAgriBiz Blog Generator
 * Plugin URI: https://github.com/autoagribiz/blog-generator
 * Description: Automatically generates SEO-optimized blog posts focused on agricultural business opportunities in developing countries, with special emphasis on Bangladesh. Features AI-powered content generation, scheduled posting, and comprehensive agricultural topic coverage.
 * Version: 1.0.0
 * Author: AutoAgriBiz Team
 * Author URI: https://autoagribiz.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: autoagribiz-blog-generator
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 * 
 * @package AutoAgriBizBlogGenerator
 * @version 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'AABG_VERSION', '1.0.0' );
define( 'AABG_PLUGIN_FILE', __FILE__ );
define( 'AABG_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'AABG_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'AABG_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

// Minimum requirements check
if ( version_compare( PHP_VERSION, '7.4', '<' ) ) {
    add_action( 'admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>AutoAgriBiz Blog Generator:</strong> This plugin requires PHP 7.4 or higher. ';
        echo 'You are running PHP ' . PHP_VERSION . '. Please upgrade your PHP version.';
        echo '</p></div>';
    });
    return;
}

if ( version_compare( get_bloginfo( 'version' ), '5.0', '<' ) ) {
    add_action( 'admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>AutoAgriBiz Blog Generator:</strong> This plugin requires WordPress 5.0 or higher. ';
        echo 'You are running WordPress ' . get_bloginfo( 'version' ) . '. Please upgrade your WordPress installation.';
        echo '</p></div>';
    });
    return;
}

// Load plugin classes
require_once AABG_PLUGIN_DIR . 'includes/class-aabg-autoloader.php';
require_once AABG_PLUGIN_DIR . 'includes/class-aabg-main.php';

add_action( 'plugins_loaded', [ 'AABG_Main', 'init' ] );

// Activation/Deactivation hooks with proper class loading
register_activation_hook( __FILE__, 'aabg_activate_plugin' );
register_deactivation_hook( __FILE__, 'aabg_deactivate_plugin' );

function aabg_activate_plugin() {
    // Ensure all classes are loaded before activation
    require_once AABG_PLUGIN_DIR . 'includes/class-aabg-scheduler.php';
    require_once AABG_PLUGIN_DIR . 'includes/class-aabg-settings.php';
    
    // Create default settings
    AABG_Settings::init_default_settings();
    
    // Schedule cron jobs
    AABG_Scheduler::activate();
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

function aabg_deactivate_plugin() {
    // Ensure scheduler class is loaded
    require_once AABG_PLUGIN_DIR . 'includes/class-aabg-scheduler.php';
    
    // Clear scheduled events
    AABG_Scheduler::deactivate();
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

// Plugin uninstall hook
register_uninstall_hook( __FILE__, 'aabg_uninstall_plugin' );

function aabg_uninstall_plugin() {
    // Only run if user has proper permissions
    if ( ! current_user_can( 'activate_plugins' ) ) {
        return;
    }
    
    // Check if we should delete data on uninstall
    $settings = get_option( 'aabg_settings', [] );
    if ( isset( $settings['delete_data_on_uninstall'] ) && $settings['delete_data_on_uninstall'] ) {
        // Delete all plugin options
        delete_option( 'aabg_settings' );
        delete_option( 'aabg_analytics' );
        delete_option( 'aabg_used_topics' );
        
        // Delete all generated posts (optional - be careful with this)
        if ( isset( $settings['delete_posts_on_uninstall'] ) && $settings['delete_posts_on_uninstall'] ) {
            $posts = get_posts([
                'post_type' => 'post',
                'meta_key' => '_aabg_generated',
                'meta_value' => '1',
                'numberposts' => -1,
                'post_status' => 'any'
            ]);
            
            foreach ( $posts as $post ) {
                wp_delete_post( $post->ID, true );
            }
        }
        
        // Clear any scheduled hooks
        wp_clear_scheduled_hook( 'aabg_generate_post' );
    }
}

// Add plugin action links
add_filter( 'plugin_action_links_' . AABG_PLUGIN_BASENAME, 'aabg_plugin_action_links' );

function aabg_plugin_action_links( $links ) {
    $settings_link = '<a href="' . admin_url( 'admin.php?page=aabg-settings' ) . '">' . __( 'Settings', 'autoagribiz-blog-generator' ) . '</a>';
    $generate_link = '<a href="' . admin_url( 'admin.php?page=aabg-generate' ) . '">' . __( 'Generate Post', 'autoagribiz-blog-generator' ) . '</a>';
    
    array_unshift( $links, $settings_link, $generate_link );
    
    return $links;
}

// Add plugin meta links
add_filter( 'plugin_row_meta', 'aabg_plugin_row_meta', 10, 2 );

function aabg_plugin_row_meta( $links, $file ) {
    if ( $file === AABG_PLUGIN_BASENAME ) {
        $links[] = '<a href="https://github.com/autoagribiz/blog-generator/wiki" target="_blank">' . __( 'Documentation', 'autoagribiz-blog-generator' ) . '</a>';
        $links[] = '<a href="https://github.com/autoagribiz/blog-generator/issues" target="_blank">' . __( 'Support', 'autoagribiz-blog-generator' ) . '</a>';
        $links[] = '<a href="https://autoagribiz.com/donate" target="_blank">' . __( 'Donate', 'autoagribiz-blog-generator' ) . '</a>';
    }
    
    return $links;
}

// Load text domain for internationalization
add_action( 'plugins_loaded', 'aabg_load_textdomain' );

function aabg_load_textdomain() {
    load_plugin_textdomain( 
        'autoagribiz-blog-generator', 
        false, 
        dirname( AABG_PLUGIN_BASENAME ) . '/languages' 
    );
}

// Add admin notices for configuration
add_action( 'admin_notices', 'aabg_admin_notices' );

function aabg_admin_notices() {
    // Only show on plugin pages
    $screen = get_current_screen();
    if ( ! $screen || strpos( $screen->id, 'aabg' ) === false ) {
        return;
    }
    
    // Check if plugin is configured
    $settings = get_option( 'aabg_settings', [] );
    
    if ( empty( $settings['ai_provider'] ) || 
         ( $settings['ai_provider'] === 'openrouter' && empty( $settings['openrouter_api_key'] ) ) ||
         ( $settings['ai_provider'] === 'openai' && empty( $settings['openai_api_key'] ) ) ||
         ( $settings['ai_provider'] === 'gemini' && empty( $settings['gemini_api_key'] ) ) ) {
        
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>' . __( 'AutoAgriBiz Blog Generator', 'autoagribiz-blog-generator' ) . ':</strong> ';
        echo __( 'Please configure your AI provider settings to start generating agricultural blog posts.', 'autoagribiz-blog-generator' );
        echo ' <a href="' . admin_url( 'admin.php?page=aabg-settings' ) . '">' . __( 'Configure Now', 'autoagribiz-blog-generator' ) . '</a>';
        echo '</p></div>';
    }
    
    if ( empty( $settings['categories'] ) ) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>' . __( 'AutoAgriBiz Blog Generator', 'autoagribiz-blog-generator' ) . ':</strong> ';
        echo __( 'Select content categories to customize your agricultural blog posts.', 'autoagribiz-blog-generator' );
        echo ' <a href="' . admin_url( 'admin.php?page=aabg-settings' ) . '">' . __( 'Select Categories', 'autoagribiz-blog-generator' ) . '</a>';
        echo '</p></div>';
    }
}

// Debug information (only in development)
if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
    add_action( 'wp_footer', 'aabg_debug_info' );
    add_action( 'admin_footer', 'aabg_debug_info' );
    
    function aabg_debug_info() {
        if ( current_user_can( 'manage_options' ) ) {
            echo '<!-- AutoAgriBiz Blog Generator Debug Info -->';
            echo '<!-- Version: ' . AABG_VERSION . ' -->';
            echo '<!-- Plugin Dir: ' . AABG_PLUGIN_DIR . ' -->';
            echo '<!-- Next Scheduled: ' . ( wp_next_scheduled( 'aabg_generate_post' ) ? date( 'Y-m-d H:i:s', wp_next_scheduled( 'aabg_generate_post' ) ) : 'Not scheduled' ) . ' -->';
            echo '<!-- End AutoAgriBiz Debug -->';
        }
    }
}

// Plugin loaded successfully
do_action( 'aabg_plugin_loaded' );
?>
