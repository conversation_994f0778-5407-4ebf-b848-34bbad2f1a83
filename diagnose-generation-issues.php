<?php
/**
 * Comprehensive diagnostic tool for blog post generation issues
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>🔍 Blog Post Generation Diagnostic</h2>";

// Check if classes exist
$required_classes = [
    'AIBPG_Settings',
    'AIBPG_Generator', 
    'AIBPG_Topic_Manager',
    'AIBPG_Content_Templates',
    'AIBPG_Content_Processor',
    'AIBPG_OpenRouter_Provider',
    'AIBPG_Scheduler'
];

echo "<h3>1. Class Availability Check</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Class</th><th>Status</th><th>File</th></tr>";

$missing_classes = [];
foreach ( $required_classes as $class ) {
    $exists = class_exists( $class );
    $status = $exists ? '✅ Available' : '❌ Missing';
    
    $file = 'Unknown';
    if ( $exists ) {
        $reflection = new ReflectionClass( $class );
        $file = basename( $reflection->getFileName() );
    }
    
    echo "<tr><td><strong>{$class}</strong></td><td>{$status}</td><td>{$file}</td></tr>";
    
    if ( !$exists ) {
        $missing_classes[] = $class;
    }
}
echo "</table>";

if ( !empty( $missing_classes ) ) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h4>❌ Critical Issue: Missing Classes</h4>";
    echo "<p>The following required classes are missing:</p>";
    echo "<ul>";
    foreach ( $missing_classes as $class ) {
        echo "<li><strong>{$class}</strong></li>";
    }
    echo "</ul>";
    echo "<p>This will prevent post generation from working.</p>";
    echo "</div>";
}

// Check settings
echo "<h3>2. Settings Configuration</h3>";
if ( class_exists( 'AIBPG_Settings' ) ) {
    $settings = AIBPG_Settings::all();
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    $ai_provider = $settings['ai_provider'] ?? 'Not set';
    $ai_status = empty( $ai_provider ) || $ai_provider === 'Not set' ? '❌ Missing' : '✅ Set';
    echo "<tr><td><strong>AI Provider</strong></td><td>{$ai_provider}</td><td>{$ai_status}</td></tr>";
    
    $api_key_field = $ai_provider . '_api_key';
    $api_key = $settings[$api_key_field] ?? '';
    $api_key_display = empty( $api_key ) ? 'Not set' : 'Set (' . strlen( $api_key ) . ' chars)';
    $api_key_status = empty( $api_key ) ? '❌ Missing' : '✅ Set';
    echo "<tr><td><strong>API Key ({$ai_provider})</strong></td><td>{$api_key_display}</td><td>{$api_key_status}</td></tr>";
    
    $categories = $settings['categories'] ?? [];
    $categories_display = empty( $categories ) ? 'None' : implode( ', ', $categories );
    $categories_status = empty( $categories ) ? '⚠️ Empty' : '✅ Set';
    echo "<tr><td><strong>Categories</strong></td><td>{$categories_display}</td><td>{$categories_status}</td></tr>";
    
    echo "</table>";
    
} else {
    echo "<p style='color: red;'>❌ AIBPG_Settings class not available</p>";
}

// Check method availability in Generator
echo "<h3>3. Generator Method Check</h3>";
if ( class_exists( 'AIBPG_Generator' ) ) {
    $required_methods = [
        'generate',
        'validate_meta',
        'get_ai_provider',
        'get_image_provider',
        'normalize_category',
        'log_error'
    ];
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Method</th><th>Status</th></tr>";
    
    $missing_methods = [];
    foreach ( $required_methods as $method ) {
        $exists = method_exists( 'AIBPG_Generator', $method );
        $status = $exists ? '✅ Available' : '❌ Missing';
        echo "<tr><td><strong>{$method}()</strong></td><td>{$status}</td></tr>";
        
        if ( !$exists ) {
            $missing_methods[] = $method;
        }
    }
    echo "</table>";
    
    if ( !empty( $missing_methods ) ) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>❌ Critical Issue: Missing Methods</h4>";
        echo "<p>The following required methods are missing from AIBPG_Generator:</p>";
        echo "<ul>";
        foreach ( $missing_methods as $method ) {
            echo "<li><strong>{$method}()</strong></li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ AIBPG_Generator class not available</p>";
}

// Test topic availability
echo "<h3>4. Topic Availability Check</h3>";
if ( class_exists( 'AIBPG_Topic_Manager' ) ) {
    $available_categories = AIBPG_Topic_Manager::get_available_categories();
    
    echo "<p><strong>Available Categories:</strong> " . implode( ', ', $available_categories ) . "</p>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Category</th><th>Topics Available</th><th>Sample Topic</th></tr>";
    
    foreach ( $available_categories as $category ) {
        $topics = AIBPG_Topic_Manager::get_all_topics( $category );
        $topic_count = count( $topics );
        $sample_topic = !empty( $topics ) ? $topics[0] : 'None';
        
        echo "<tr><td><strong>{$category}</strong></td><td>{$topic_count}</td><td>{$sample_topic}</td></tr>";
    }
    echo "</table>";
    
} else {
    echo "<p style='color: red;'>❌ AIBPG_Topic_Manager class not available</p>";
}

// Test API connectivity
echo "<h3>5. API Connectivity Test</h3>";
if ( class_exists( 'AIBPG_Settings' ) && class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    $settings = AIBPG_Settings::all();
    
    if ( $settings['ai_provider'] === 'openrouter' && !empty( $settings['openrouter_api_key'] ) ) {
        try {
            $provider = new AIBPG_OpenRouter_Provider();
            $test_result = $provider->test_connection();
            
            if ( $test_result['success'] ) {
                echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                echo "<h4>✅ API Connection Successful</h4>";
                echo "<p><strong>Message:</strong> {$test_result['message']}</p>";
                echo "<p><strong>Model:</strong> {$test_result['model']}</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
                echo "<h4>❌ API Connection Failed</h4>";
                echo "<p><strong>Error:</strong> {$test_result['message']}</p>";
                echo "</div>";
            }
        } catch ( Exception $e ) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ API Test Error</h4>";
            echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ OpenRouter not configured - cannot test API</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Cannot test API - required classes missing</p>";
}

// Test scheduler
echo "<h3>6. Scheduler Status</h3>";
if ( class_exists( 'AIBPG_Scheduler' ) ) {
    $next_scheduled = AIBPG_Scheduler::get_next_scheduled();
    
    if ( $next_scheduled ) {
        echo "<p>✅ <strong>Cron Scheduled:</strong> " . date( 'Y-m-d H:i:s', $next_scheduled ) . "</p>";
    } else {
        echo "<p>❌ <strong>Cron Not Scheduled</strong></p>";
    }
    
    // Check if WordPress cron is working
    $cron_jobs = wp_get_ready_cron_jobs();
    $aibpg_jobs = 0;
    foreach ( $cron_jobs as $timestamp => $cron ) {
        foreach ( $cron as $hook => $events ) {
            if ( strpos( $hook, 'aibpg' ) !== false ) {
                $aibpg_jobs++;
            }
        }
    }
    
    echo "<p><strong>AIBPG Cron Jobs:</strong> {$aibpg_jobs}</p>";
    
} else {
    echo "<p style='color: red;'>❌ AIBPG_Scheduler class not available</p>";
}

// Attempt a test generation
echo "<h3>7. Test Generation Attempt</h3>";
if ( class_exists( 'AIBPG_Generator' ) && method_exists( 'AIBPG_Generator', 'generate' ) ) {
    echo "<p>Attempting to generate a test post...</p>";
    
    try {
        // Capture any errors
        ob_start();
        $result = AIBPG_Generator::generate();
        $output = ob_get_clean();
        
        if ( $result ) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Test Generation Successful</h4>";
            echo "<p><strong>Post ID:</strong> {$result}</p>";
            $post = get_post( $result );
            if ( $post ) {
                echo "<p><strong>Title:</strong> {$post->post_title}</p>";
                echo "<p><strong>Status:</strong> {$post->post_status}</p>";
                echo "<p><strong>Content Length:</strong> " . strlen( $post->post_content ) . " characters</p>";
            }
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Test Generation Failed</h4>";
            echo "<p>No post was created. Check the issues above.</p>";
            if ( !empty( $output ) ) {
                echo "<p><strong>Output:</strong> " . esc_html( $output ) . "</p>";
            }
            echo "</div>";
        }
        
    } catch ( Exception $e ) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Test Generation Error</h4>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>File:</strong> " . $e->getFile() . ":" . $e->getLine() . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Cannot test generation - AIBPG_Generator not available or missing generate method</p>";
}

// Summary and recommendations
echo "<h3>8. Summary & Recommendations</h3>";

$issues = [];
if ( !empty( $missing_classes ) ) {
    $issues[] = "Missing required classes: " . implode( ', ', $missing_classes );
}
if ( !empty( $missing_methods ) ) {
    $issues[] = "Missing required methods: " . implode( ', ', $missing_methods );
}
if ( !class_exists( 'AIBPG_Settings' ) || empty( AIBPG_Settings::get( 'ai_provider' ) ) ) {
    $issues[] = "AI provider not configured";
}

if ( empty( $issues ) ) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ No Critical Issues Found</h4>";
    echo "<p>The plugin should be working. If posts aren't being generated, check:</p>";
    echo "<ul>";
    echo "<li>WordPress cron is running</li>";
    echo "<li>API rate limits</li>";
    echo "<li>Server error logs</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Critical Issues Found</h4>";
    echo "<p>The following issues are preventing post generation:</p>";
    echo "<ul>";
    foreach ( $issues as $issue ) {
        echo "<li>{$issue}</li>";
    }
    echo "</ul>";
    echo "<p><strong>These must be fixed before the plugin can work.</strong></p>";
    echo "</div>";
}

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Fix any missing classes or methods identified above</li>";
echo "<li>Ensure AI provider is properly configured</li>";
echo "<li>Test manual generation from the admin interface</li>";
echo "<li>Check WordPress error logs for additional details</li>";
echo "<li>Verify cron jobs are scheduled and running</li>";
echo "</ol>";
?>
