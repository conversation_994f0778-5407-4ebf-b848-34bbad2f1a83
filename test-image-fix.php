<?php
/**
 * Quick test to verify the image download fix works
 * Run this from WordPress admin to test the fix
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>Testing Image Download Fix</h2>";

// Test if WordPress admin functions are available
$functions_to_test = [
    'download_url',
    'media_handle_sideload', 
    'wp_generate_attachment_metadata',
    'wp_upload_bits',
    'wp_check_filetype',
    'wp_insert_attachment'
];

echo "<h3>WordPress Function Availability:</h3>";
echo "<ul>";

foreach ( $functions_to_test as $function ) {
    $available = function_exists( $function );
    $status = $available ? '✅ Available' : '❌ Missing';
    echo "<li><strong>{$function}</strong>: {$status}</li>";
    
    if ( !$available ) {
        // Try to load the function
        if ( $function === 'download_url' && file_exists( ABSPATH . 'wp-admin/includes/file.php' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/file.php' );
        }
        if ( $function === 'media_handle_sideload' && file_exists( ABSPATH . 'wp-admin/includes/media.php' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/media.php' );
        }
        if ( $function === 'wp_generate_attachment_metadata' && file_exists( ABSPATH . 'wp-admin/includes/image.php' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/image.php' );
        }
        
        $now_available = function_exists( $function );
        if ( $now_available ) {
            echo " → ✅ Loaded successfully";
        }
    }
}

echo "</ul>";

// Test image processing capability
echo "<h3>Image Processing Test:</h3>";

// Test URL (small test image)
$test_image_url = 'https://via.placeholder.com/150x150.jpg';

echo "<p>Testing with: <a href='{$test_image_url}' target='_blank'>{$test_image_url}</a></p>";

// Test wp_remote_get
$image_data = wp_remote_get( $test_image_url, [ 'timeout' => 10 ] );
if ( is_wp_error( $image_data ) ) {
    echo "<p>❌ wp_remote_get failed: " . $image_data->get_error_message() . "</p>";
} else {
    $response_code = wp_remote_retrieve_response_code( $image_data );
    echo "<p>✅ wp_remote_get successful (HTTP {$response_code})</p>";
    
    $image_body = wp_remote_retrieve_body( $image_data );
    $body_size = strlen( $image_body );
    echo "<p>✅ Image data retrieved: {$body_size} bytes</p>";
}

// Test wp_upload_bits
if ( !empty( $image_body ) ) {
    $upload = wp_upload_bits( 'test-image.jpg', null, $image_body );
    if ( $upload['error'] ) {
        echo "<p>❌ wp_upload_bits failed: " . $upload['error'] . "</p>";
    } else {
        echo "<p>✅ wp_upload_bits successful: " . $upload['file'] . "</p>";
        
        // Clean up test file
        if ( file_exists( $upload['file'] ) ) {
            unlink( $upload['file'] );
            echo "<p>🧹 Test file cleaned up</p>";
        }
    }
}

// Test the actual generator method (if available)
if ( class_exists( 'AIBPG_Generator' ) ) {
    echo "<h3>Generator Class Test:</h3>";
    
    // Create a test post
    $test_post_id = wp_insert_post([
        'post_title' => 'Test Post for Image Fix',
        'post_content' => 'This is a test post to verify image functionality.',
        'post_status' => 'draft',
        'post_type' => 'post'
    ]);
    
    if ( $test_post_id && !is_wp_error( $test_post_id ) ) {
        echo "<p>✅ Test post created (ID: {$test_post_id})</p>";
        
        // Use reflection to test the private method
        $reflection = new ReflectionClass( 'AIBPG_Generator' );
        $method = $reflection->getMethod( 'set_featured_image' );
        $method->setAccessible( true );
        
        try {
            $result = $method->invoke( null, $test_post_id, $test_image_url );
            if ( $result ) {
                echo "<p>✅ set_featured_image method successful (Attachment ID: {$result})</p>";
                
                // Check if thumbnail was set
                $thumbnail_id = get_post_thumbnail_id( $test_post_id );
                if ( $thumbnail_id ) {
                    echo "<p>✅ Featured image set successfully (Thumbnail ID: {$thumbnail_id})</p>";
                } else {
                    echo "<p>⚠️ Featured image not set (but method returned success)</p>";
                }
            } else {
                echo "<p>❌ set_featured_image method returned false</p>";
            }
        } catch ( Exception $e ) {
            echo "<p>❌ set_featured_image method threw exception: " . $e->getMessage() . "</p>";
        }
        
        // Clean up test post
        wp_delete_post( $test_post_id, true );
        echo "<p>🧹 Test post cleaned up</p>";
        
    } else {
        echo "<p>❌ Failed to create test post</p>";
    }
} else {
    echo "<p>⚠️ AIBPG_Generator class not available</p>";
}

echo "<h3>Summary:</h3>";
echo "<p>If all tests show ✅, the image download fix should work correctly during cron execution.</p>";
echo "<p>If you see ❌ errors, there may still be issues that need addressing.</p>";

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Try generating a post manually to test the full workflow</li>";
echo "<li>Check the error logs for any remaining issues</li>";
echo "<li>Monitor automatic post generation via cron</li>";
echo "</ul>";
?>
