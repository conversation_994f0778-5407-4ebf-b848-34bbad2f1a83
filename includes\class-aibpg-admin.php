<?php
/**
 * Admin UI for AI Blog Post Generator
 */
class AIBPG_Admin {
    public static function init() {
        add_action( 'admin_menu', [ __CLASS__, 'add_menu' ] );
        add_action( 'admin_init', [ __CLASS__, 'register_settings' ] );
        add_action( 'admin_init', [ __CLASS__, 'handle_actions' ] );
        add_action( 'admin_enqueue_scripts', [ __CLASS__, 'enqueue_scripts' ] );
        add_action( 'wp_ajax_aibpg_manual_generate', [ __CLASS__, 'ajax_manual_generate' ] );
        add_action( 'wp_ajax_aibpg_test_api', [ __CLASS__, 'ajax_test_api' ] );
        add_action( 'wp_ajax_aibpg_test_current_connection', [ __CLASS__, 'ajax_test_current_connection' ] );
        add_action( 'wp_ajax_aibpg_export_analytics', [ __CLASS__, 'ajax_export_analytics' ] );
    }

    public static function add_menu() {
        $main_page = add_menu_page(
            'AI Blog Post Generator',
            'AI Blog Post Generator',
            'manage_options',
            'aibpg',
            [ __CLASS__, 'settings_page' ],
            'dashicons-edit',
            80
        );

        add_submenu_page(
            'aibpg',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'aibpg',
            [ __CLASS__, 'dashboard_page' ]
        );

        add_submenu_page(
            'aibpg',
            'Settings',
            'Settings',
            'manage_options',
            'aibpg-settings',
            [ __CLASS__, 'settings_page' ]
        );

        add_submenu_page(
            'aibpg',
            'Generate Post',
            'Generate Post',
            'manage_options',
            'aibpg-generate',
            [ __CLASS__, 'generate_page' ]
        );

        add_submenu_page(
            'aibpg',
            'Logs',
            'Logs',
            'manage_options',
            'aibpg-logs',
            [ __CLASS__, 'logs_page' ]
        );

        add_submenu_page(
            'aibpg',
            'Status',
            'Status',
            'manage_options',
            'aibpg-status',
            [ __CLASS__, 'status_page' ]
        );

        add_submenu_page(
            'aibpg',
            'Security',
            'Security',
            'manage_options',
            'aibpg-security',
            [ __CLASS__, 'security_page' ]
        );
    }

    public static function register_settings() {
        register_setting( 'aibpg_options', 'aibpg_options', [ __CLASS__, 'sanitize_settings' ] );
    }

    public static function sanitize_settings( $input ) {
        // Security validation
        AIBPG_Security::validate_user_permissions();

        $sanitized = [];

        // Sanitize text fields
        $text_fields = [ 'openai_api_key', 'gemini_api_key', 'openrouter_api_key', 'pexels_api_key' ];
        foreach ( $text_fields as $field ) {
            $sanitized[ $field ] = sanitize_text_field( $input[ $field ] ?? '' );
        }

        // Sanitize select fields
        $sanitized['ai_provider'] = in_array( $input['ai_provider'] ?? '', [ 'openai', 'gemini', 'openrouter' ] ) ? $input['ai_provider'] : 'openrouter';
        $sanitized['openrouter_model'] = sanitize_text_field( $input['openrouter_model'] ?? 'mistralai/mistral-7b-instruct:free' );
        $sanitized['image_provider'] = in_array( $input['image_provider'] ?? '', [ 'pexels', 'none' ] ) ? $input['image_provider'] : 'pexels';
        $sanitized['post_status'] = in_array( $input['post_status'] ?? '', [ 'draft', 'publish' ] ) ? $input['post_status'] : 'draft';
        $sanitized['post_frequency'] = in_array( $input['post_frequency'] ?? '', [ 'daily', 'weekly' ] ) ? $input['post_frequency'] : 'daily';

        // Sanitize arrays
        $sanitized['categories'] = array_map( 'sanitize_text_field', $input['categories'] ?? [] );
        $sanitized['wp_categories'] = array_map( 'sanitize_text_field', $input['wp_categories'] ?? [] );
        $sanitized['languages'] = array_map( 'sanitize_text_field', $input['languages'] ?? [ 'en' ] );

        // Validate plugin categories against available ones
        $available_categories = AIBPG_Topic_Manager::get_available_categories();
        $sanitized['categories'] = array_intersect( $sanitized['categories'], $available_categories );

        // Validate settings using security class
        $validation = AIBPG_Security::validate_settings( $sanitized );
        if ( $validation !== true ) {
            add_settings_error( 'aibpg_options', 'validation_error', implode( '<br>', $validation ) );
            AIBPG_Security::log_security_event( 'invalid_settings', [ 'errors' => $validation ] );
        }

        // Update scheduler if frequency changed
        $old_settings = AIBPG_Settings::all();
        if ( $old_settings['post_frequency'] !== $sanitized['post_frequency'] ) {
            AIBPG_Scheduler::schedule_next_event();
        }

        // Log settings change
        AIBPG_Security::log_security_event( 'settings_updated', [ 'changed_fields' => array_keys( $sanitized ) ] );

        return $sanitized;
    }

    public static function settings_page() {
        $options = AIBPG_Settings::all();
        $available_categories = AIBPG_Topic_Manager::get_available_categories();
        $wp_categories = get_categories( [ 'hide_empty' => false ] );
        ?>
        <div class="wrap">
            <h1>AI Blog Post Generator Settings</h1>

            <?php if ( isset( $_GET['settings-updated'] ) ): ?>
                <div class="notice notice-success is-dismissible">
                    <p>Settings saved successfully!</p>
                </div>
            <?php endif; ?>

            <form method="post" action="options.php">
                <?php settings_fields( 'aibpg_options' ); ?>

                <h2 class="nav-tab-wrapper">
                    <a href="#ai-settings" class="nav-tab nav-tab-active">AI Settings</a>
                    <a href="#content-settings" class="nav-tab">Content Settings</a>
                    <a href="#scheduling" class="nav-tab">Scheduling</a>
                </h2>

                <div id="ai-settings" class="tab-content">
                    <h3>AI Provider Configuration</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">AI Provider</th>
                            <td>
                                <select name="aibpg_options[ai_provider]" id="ai_provider">
                                    <option value="openrouter" <?php selected( $options['ai_provider'], 'openrouter' ); ?>>OpenRouter (Free Models)</option>
                                    <option value="openai" <?php selected( $options['ai_provider'], 'openai' ); ?>>OpenAI (GPT-3.5)</option>
                                    <option value="gemini" <?php selected( $options['ai_provider'], 'gemini' ); ?>>Google Gemini</option>
                                </select>
                                <p class="description">Choose your preferred AI provider. OpenRouter offers free models!</p>
                            </td>
                        </tr>
                        <tr class="openrouter-settings">
                            <th scope="row">OpenRouter API Key</th>
                            <td>
                                <input type="password" name="aibpg_options[openrouter_api_key]" value="<?php echo esc_attr( $options['openrouter_api_key'] ); ?>" size="50" class="regular-text">
                                <button type="button" class="button test-api-btn" data-provider="openrouter">Test Connection</button>
                                <p class="description">Get your FREE API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a> - No credit card required!</p>
                            </td>
                        </tr>
                        <tr class="openrouter-settings">
                            <th scope="row">OpenRouter Model</th>
                            <td>
                                <select name="aibpg_options[openrouter_model]" id="openrouter_model">
                                    <?php
                                    $available_models = AIBPG_OpenRouter_Provider::get_available_models();
                                    $current_model = $options['openrouter_model'] ?? 'mistralai/mistral-7b-instruct:free';
                                    foreach ( $available_models as $model_id => $model_info ):
                                    ?>
                                        <option value="<?php echo esc_attr( $model_id ); ?>" <?php selected( $current_model, $model_id ); ?>>
                                            <?php echo esc_html( $model_info['name'] ); ?>
                                            <?php if ( $model_info['free'] ): ?>
                                                <span style="color: green;">(FREE)</span>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <p class="description">Choose from available models. Free models are marked in green!</p>
                                <div id="model-info" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border-left: 4px solid #0073aa; display: none;">
                                    <strong>Model Info:</strong> <span id="model-description"></span><br>
                                    <strong>Max Tokens:</strong> <span id="model-max-tokens"></span>
                                </div>
                            </td>
                        </tr>
                        <tr class="openai-settings">
                            <th scope="row">OpenAI API Key</th>
                            <td>
                                <input type="password" name="aibpg_options[openai_api_key]" value="<?php echo esc_attr( $options['openai_api_key'] ); ?>" size="50" class="regular-text">
                                <button type="button" class="button test-api-btn" data-provider="openai">Test Connection</button>
                                <p class="description">Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a></p>
                            </td>
                        </tr>
                        <tr class="gemini-settings">
                            <th scope="row">Gemini API Key</th>
                            <td>
                                <input type="password" name="aibpg_options[gemini_api_key]" value="<?php echo esc_attr( $options['gemini_api_key'] ); ?>" size="50" class="regular-text">
                                <button type="button" class="button test-api-btn" data-provider="gemini">Test Connection</button>
                                <p class="description">Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></p>
                            </td>
                        </tr>
                    </table>

                    <h3>Image Provider Configuration</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">Image Provider</th>
                            <td>
                                <select name="aibpg_options[image_provider]">
                                    <option value="pexels" <?php selected( $options['image_provider'], 'pexels' ); ?>>Pexels</option>
                                    <option value="none" <?php selected( $options['image_provider'], 'none' ); ?>>None (No Images)</option>
                                </select>
                                <p class="description">Choose image provider for featured images.</p>
                            </td>
                        </tr>
                        <tr class="pexels-settings">
                            <th scope="row">Pexels API Key</th>
                            <td>
                                <input type="password" name="aibpg_options[pexels_api_key]" value="<?php echo esc_attr( $options['pexels_api_key'] ); ?>" size="50" class="regular-text">
                                <p class="description">Get your API key from <a href="https://www.pexels.com/api/" target="_blank">Pexels API</a></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div id="content-settings" class="tab-content" style="display:none;">
                    <h3>Content Configuration</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">Content Categories</th>
                            <td>
                                <div style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9;">
                                    <h4 style="margin-top: 0;">Plugin Categories (for content generation):</h4>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 15px;">
                                        <?php foreach ( $available_categories as $category ): ?>
                                            <label style="display: flex; align-items: center; padding: 8px; background: white; border: 1px solid #ddd; border-radius: 4px;">
                                                <input type="checkbox" name="aibpg_options[categories][]" value="<?php echo esc_attr( $category ); ?>"
                                                       <?php checked( in_array( $category, $options['categories'] ) ); ?> style="margin-right: 8px;">
                                                <strong><?php echo esc_html( ucfirst( $category ) ); ?></strong>
                                            </label>
                                        <?php endforeach; ?>
                                    </div>
                                    <p class="description">
                                        <strong>These are content categories with pre-built topics and templates.</strong><br>
                                        Available: <?php echo implode( ', ', array_map( 'ucfirst', $available_categories ) ); ?>
                                    </p>
                                </div>

                                <div style="border: 1px solid #ddd; padding: 15px; margin-top: 15px; background: #fff;">
                                    <h4 style="margin-top: 0;">WordPress Categories (for post assignment):</h4>
                                    <div style="max-height: 150px; overflow-y: auto;">
                                        <?php foreach ( $wp_categories as $wp_category ): ?>
                                            <label style="display: block; margin-bottom: 5px;">
                                                <input type="checkbox" name="aibpg_options[wp_categories][]" value="<?php echo esc_attr( $wp_category->slug ); ?>"
                                                       <?php checked( in_array( $wp_category->slug, $options['wp_categories'] ?? [] ) ); ?>>
                                                <?php echo esc_html( $wp_category->name ); ?>
                                            </label>
                                        <?php endforeach; ?>
                                    </div>
                                    <p class="description">
                                        <strong>Optional:</strong> Assign generated posts to these WordPress categories.<br>
                                        If none selected, posts will be uncategorized.
                                    </p>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Languages</th>
                            <td>
                                <select name="aibpg_options[languages][]" multiple size="5">
                                    <option value="en" <?php selected( in_array( 'en', $options['languages'] ) ); ?>>English</option>
                                    <option value="es" <?php selected( in_array( 'es', $options['languages'] ) ); ?>>Spanish</option>
                                    <option value="fr" <?php selected( in_array( 'fr', $options['languages'] ) ); ?>>French</option>
                                    <option value="de" <?php selected( in_array( 'de', $options['languages'] ) ); ?>>German</option>
                                    <option value="it" <?php selected( in_array( 'it', $options['languages'] ) ); ?>>Italian</option>
                                </select>
                                <p class="description">Hold Ctrl/Cmd to select multiple languages.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Post Status</th>
                            <td>
                                <select name="aibpg_options[post_status]">
                                    <option value="draft" <?php selected( $options['post_status'], 'draft' ); ?>>Draft</option>
                                    <option value="publish" <?php selected( $options['post_status'], 'publish' ); ?>>Publish Immediately</option>
                                </select>
                                <p class="description">Status for generated posts.</p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div id="scheduling" class="tab-content" style="display:none;">
                    <h3>Scheduling Configuration</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">Post Frequency</th>
                            <td>
                                <select name="aibpg_options[post_frequency]">
                                    <option value="daily" <?php selected( $options['post_frequency'], 'daily' ); ?>>Daily</option>
                                    <option value="weekly" <?php selected( $options['post_frequency'], 'weekly' ); ?>>Weekly</option>
                                </select>
                                <p class="description">How often to automatically generate new posts.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Next Scheduled</th>
                            <td>
                                <?php
                                $next_scheduled = AIBPG_Scheduler::get_next_scheduled();
                                if ( $next_scheduled ) {
                                    echo '<strong>' . date( 'Y-m-d H:i:s', $next_scheduled ) . '</strong>';
                                } else {
                                    echo '<em>Not scheduled</em>';
                                }
                                ?>
                                <p class="description">Next automatic post generation time.</p>
                            </td>
                        </tr>
                    </table>
                </div>

                <?php submit_button(); ?>
            </form>
        </div>

        <style>
        .tab-content { margin-top: 20px; }
        .nav-tab-wrapper { margin-bottom: 0; }
        .test-api-btn { margin-left: 10px; }
        .api-test-result { margin-top: 5px; padding: 5px; border-radius: 3px; }
        .api-test-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .api-test-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Tab switching
            $('.nav-tab').click(function(e) {
                e.preventDefault();
                $('.nav-tab').removeClass('nav-tab-active');
                $(this).addClass('nav-tab-active');
                $('.tab-content').hide();
                $($(this).attr('href')).show();
            });

            // Show/hide provider settings
            function toggleProviderSettings() {
                var provider = $('#ai_provider').val();
                $('.openai-settings, .gemini-settings, .openrouter-settings').hide();
                $('.' + provider + '-settings').show();

                var imageProvider = $('select[name="aibpg_options[image_provider]"]').val();
                $('.pexels-settings').toggle(imageProvider === 'pexels');

                // Show model info for OpenRouter
                if (provider === 'openrouter') {
                    updateModelInfo();
                }
            }

            // Update model information display
            function updateModelInfo() {
                var selectedModel = $('#openrouter_model').val();
                var models = <?php echo wp_json_encode( AIBPG_OpenRouter_Provider::get_available_models() ); ?>;

                if (models[selectedModel]) {
                    var modelInfo = models[selectedModel];
                    $('#model-description').text(modelInfo.description);
                    $('#model-max-tokens').text(modelInfo.max_tokens === 'unlimited' ? 'Unlimited' : modelInfo.max_tokens);
                    $('#model-info').show();
                } else {
                    $('#model-info').hide();
                }
            }

            // Handle model selection change
            $('#openrouter_model').change(updateModelInfo);

            $('#ai_provider, select[name="aibpg_options[image_provider]"]').change(toggleProviderSettings);
            toggleProviderSettings();

            // API testing
            $('.test-api-btn').click(function() {
                var btn = $(this);
                var provider = btn.data('provider');
                var apiKey = $('input[name="aibpg_options[' + provider + '_api_key]"]').val();

                if (!apiKey) {
                    alert('Please enter an API key first.');
                    return;
                }

                btn.prop('disabled', true).text('Testing...');

                var postData = {
                    action: 'aibpg_test_api',
                    provider: provider,
                    api_key: apiKey,
                    nonce: '<?php echo wp_create_nonce( "aibpg_test_api" ); ?>'
                };

                // Include model for OpenRouter
                if (provider === 'openrouter') {
                    postData.model = $('#openrouter_model').val();
                }

                $.post(ajaxurl, postData, function(response) {
                    btn.prop('disabled', false).text('Test Connection');

                    var resultDiv = btn.siblings('.api-test-result');
                    if (resultDiv.length === 0) {
                        resultDiv = $('<div class="api-test-result"></div>');
                        btn.after(resultDiv);
                    }

                    if (response.success) {
                        resultDiv.removeClass('api-test-error').addClass('api-test-success').text('✓ Connection successful!');
                    } else {
                        resultDiv.removeClass('api-test-success').addClass('api-test-error').text('✗ ' + response.data);
                    }

                    setTimeout(function() {
                        resultDiv.fadeOut();
                    }, 5000);
                });
            });
        });
        </script>
        <?php
    }

    public static function dashboard_page() {
        $stats = AIBPG_Analytics::get_dashboard_stats();
        $trending_topics = AIBPG_Analytics::get_trending_topics();
        $category_performance = AIBPG_Analytics::get_category_performance();

        ?>
        <div class="wrap">
            <h1>AI Blog Post Generator Dashboard</h1>

            <!-- Stats Cards -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="card">
                    <h3>Total Posts Generated</h3>
                    <p style="font-size: 36px; margin: 10px 0; color: #0073aa;"><strong><?php echo $stats['total_posts']; ?></strong></p>
                    <small>All time</small>
                </div>

                <div class="card">
                    <h3>This Month</h3>
                    <p style="font-size: 36px; margin: 10px 0; color: #00a32a;"><strong><?php echo $stats['posts_this_month']; ?></strong></p>
                    <small>Posts generated</small>
                </div>

                <div class="card">
                    <h3>Success Rate</h3>
                    <p style="font-size: 36px; margin: 10px 0; color: <?php echo $stats['success_rate'] >= 80 ? '#00a32a' : '#d63638'; ?>;"><strong><?php echo $stats['success_rate']; ?>%</strong></p>
                    <small>Generation success</small>
                </div>

                <div class="card">
                    <h3>Average per Day</h3>
                    <p style="font-size: 36px; margin: 10px 0; color: #0073aa;"><strong><?php echo $stats['avg_posts_per_day']; ?></strong></p>
                    <small>Last 30 days</small>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                <!-- Recent Activity -->
                <div class="card">
                    <h3>Recent Activity (Last 7 Days)</h3>
                    <?php if ( !empty( $stats['recent_activity'] ) ): ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr><th>Date</th><th>Posts</th><th>Categories</th></tr>
                            </thead>
                            <tbody>
                                <?php foreach ( array_reverse( $stats['recent_activity'], true ) as $date => $data ): ?>
                                    <tr>
                                        <td><?php echo date( 'M j', strtotime( $date ) ); ?></td>
                                        <td><strong><?php echo $data['posts_generated']; ?></strong></td>
                                        <td><?php echo implode( ', ', array_keys( $data['categories'] ) ); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p><em>No recent activity</em></p>
                    <?php endif; ?>
                </div>

                <!-- Category Breakdown -->
                <div class="card">
                    <h3>Category Performance</h3>
                    <?php if ( !empty( $category_performance ) ): ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr><th>Category</th><th>Posts</th><th>Avg Views</th></tr>
                            </thead>
                            <tbody>
                                <?php foreach ( $category_performance as $category => $data ): ?>
                                    <tr>
                                        <td><strong><?php echo ucfirst( $category ); ?></strong></td>
                                        <td><?php echo $data['posts']; ?></td>
                                        <td><?php echo $data['avg_views']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p><em>No category data available</em></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Trending Topics -->
            <div class="card">
                <h3>Trending Topics (Last 30 Days)</h3>
                <?php if ( !empty( $trending_topics ) ): ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr><th>Topic</th><th>Posts</th><th>Views</th><th>Comments</th><th>Engagement Score</th></tr>
                        </thead>
                        <tbody>
                            <?php foreach ( $trending_topics as $topic => $data ): ?>
                                <tr>
                                    <td><strong><?php echo esc_html( $topic ); ?></strong></td>
                                    <td><?php echo $data['posts']; ?></td>
                                    <td><?php echo $data['views']; ?></td>
                                    <td><?php echo $data['comments']; ?></td>
                                    <td><strong><?php echo $data['score']; ?></strong></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p><em>No trending topics data available</em></p>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="card" style="margin-top: 20px;">
                <h3>Quick Actions</h3>
                <p>
                    <a href="<?php echo admin_url( 'admin.php?page=aibpg-generate' ); ?>" class="button button-primary">Generate New Post</a>
                    <a href="<?php echo admin_url( 'admin.php?page=aibpg-settings' ); ?>" class="button">Settings</a>
                    <a href="<?php echo admin_url( 'admin.php?page=aibpg-logs' ); ?>" class="button">View Logs</a>
                    <button type="button" id="export-analytics" class="button">Export Analytics</button>
                </p>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#export-analytics').click(function() {
                window.location.href = ajaxurl + '?action=aibpg_export_analytics&format=csv&nonce=<?php echo wp_create_nonce( "aibpg_export" ); ?>';
            });
        });
        </script>
        <?php
    }

    public static function generate_page() {
        ?>
        <div class="wrap">
            <h1>Generate Post</h1>

            <div class="card">
                <h2>Manual Post Generation</h2>
                <p>Generate a new blog post immediately using your current settings.</p>

                <button type="button" id="manual-generate-btn" class="button button-primary button-large">
                    Generate New Post
                </button>

                <div id="generation-result" style="margin-top: 20px;"></div>
            </div>

            <div class="card" style="margin-top: 20px;">
                <h2>Recent Generated Posts</h2>
                <?php self::display_recent_posts(); ?>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#manual-generate-btn').click(function() {
                var btn = $(this);
                var resultDiv = $('#generation-result');

                btn.prop('disabled', true).text('Generating...');
                resultDiv.html('<div class="notice notice-info"><p>Generating post, please wait...</p></div>');

                $.post(ajaxurl, {
                    action: 'aibpg_manual_generate',
                    nonce: '<?php echo wp_create_nonce( "aibpg_manual_generate" ); ?>'
                }, function(response) {
                    btn.prop('disabled', false).text('Generate New Post');

                    if (response.success) {
                        resultDiv.html('<div class="notice notice-success"><p>✓ ' + response.data + '</p></div>');
                        // Refresh the recent posts section
                        location.reload();
                    } else {
                        resultDiv.html('<div class="notice notice-error"><p>✗ ' + response.data + '</p></div>');
                    }
                });
            });
        });
        </script>
        <?php
    }

    public static function logs_page() {
        $error_logs = get_option( 'aibpg_error_logs', [] );
        $success_logs = get_option( 'aibpg_success_logs', [] );

        // Combine and sort logs
        $all_logs = array_merge( $error_logs, $success_logs );
        usort( $all_logs, function( $a, $b ) {
            return strtotime( $b['timestamp'] ) - strtotime( $a['timestamp'] );
        });

        ?>
        <div class="wrap">
            <h1>Activity Logs</h1>

            <div class="tablenav top">
                <div class="alignleft actions">
                    <a href="<?php echo admin_url( 'admin.php?page=aibpg-logs&action=clear_logs' ); ?>"
                       class="button" onclick="return confirm('Are you sure you want to clear all logs?')">
                        Clear All Logs
                    </a>
                </div>
            </div>

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col" style="width: 100px;">Type</th>
                        <th scope="col" style="width: 150px;">Time</th>
                        <th scope="col">Message</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( empty( $all_logs ) ): ?>
                        <tr>
                            <td colspan="3" style="text-align: center; padding: 20px;">
                                <em>No logs available</em>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ( array_slice( $all_logs, 0, 100 ) as $log ): ?>
                            <tr>
                                <td>
                                    <span class="log-type log-<?php echo esc_attr( $log['type'] ); ?>">
                                        <?php echo $log['type'] === 'error' ? '✗ Error' : '✓ Success'; ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html( date( 'M j, Y H:i', strtotime( $log['timestamp'] ) ) ); ?></td>
                                <td><?php echo esc_html( $log['message'] ); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <style>
        .log-type { padding: 3px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-success { background: #d4edda; color: #155724; }
        </style>
        <?php
    }

    public static function status_page() {
        $settings = AIBPG_Settings::all();
        $next_scheduled = AIBPG_Scheduler::get_next_scheduled();

        // Get recent posts
        $recent_posts = get_posts([
            'meta_key' => '_aibpg_generated',
            'meta_value' => true,
            'numberposts' => 5,
            'post_status' => 'any'
        ]);

        // Check API status
        $api_status = self::check_api_status( $settings );

        ?>
        <div class="wrap">
            <h1>Plugin Status</h1>

            <div class="card">
                <h2>Configuration Status</h2>
                <table class="form-table">
                    <tr>
                        <th>AI Provider</th>
                        <td>
                            <strong><?php echo esc_html( ucfirst( $settings['ai_provider'] ) ); ?></strong>
                            <?php if ( $api_status['ai_configured'] ): ?>
                                <span style="color: green;">✓ Configured</span>
                                <button type="button" id="test-current-connection" class="button button-small" style="margin-left: 10px;">Test Connection</button>
                                <?php if ( $settings['ai_provider'] === 'openrouter' ): ?>
                                    <br><small>Model: <?php echo esc_html( $settings['openrouter_model'] ?? 'mistralai/mistral-7b-instruct:free' ); ?></small>
                                <?php endif; ?>
                                <div id="connection-test-result" style="margin-top: 10px; display: none;"></div>
                            <?php else: ?>
                                <span style="color: red;">✗ Not configured</span>
                                <?php if ( $settings['ai_provider'] === 'openrouter' ): ?>
                                    <br><small>Missing OpenRouter API key - <a href="<?php echo admin_url( 'admin.php?page=aibpg-settings' ); ?>">Configure Now</a></small>
                                <?php elseif ( $settings['ai_provider'] === 'openai' ): ?>
                                    <br><small>Missing OpenAI API key - <a href="<?php echo admin_url( 'admin.php?page=aibpg-settings' ); ?>">Configure Now</a></small>
                                <?php elseif ( $settings['ai_provider'] === 'gemini' ): ?>
                                    <br><small>Missing Gemini API key - <a href="<?php echo admin_url( 'admin.php?page=aibpg-settings' ); ?>">Configure Now</a></small>
                                <?php endif; ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Image Provider</th>
                        <td>
                            <strong><?php echo esc_html( ucfirst( $settings['image_provider'] ) ); ?></strong>
                            <?php if ( $settings['image_provider'] === 'none' || $api_status['image_configured'] ): ?>
                                <span style="color: green;">✓ Configured</span>
                            <?php else: ?>
                                <span style="color: red;">✗ Not configured</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Scheduling</th>
                        <td>
                            <strong><?php echo esc_html( ucfirst( $settings['post_frequency'] ) ); ?></strong>
                            <?php if ( $next_scheduled ): ?>
                                <br><small>Next: <?php echo date( 'Y-m-d H:i:s', $next_scheduled ); ?></small>
                            <?php else: ?>
                                <br><small style="color: red;">Not scheduled</small>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="card" style="margin-top: 20px;">
                <h2>Statistics</h2>
                <div style="display: flex; gap: 20px;">
                    <div style="flex: 1;">
                        <h3>Generated Posts</h3>
                        <p style="font-size: 24px; margin: 0;"><strong><?php echo count( $recent_posts ); ?></strong></p>
                        <small>Last 5 posts</small>
                    </div>
                    <div style="flex: 1;">
                        <h3>Success Rate</h3>
                        <?php
                        $success_logs = get_option( 'aibpg_success_logs', [] );
                        $error_logs = get_option( 'aibpg_error_logs', [] );
                        $total = count( $success_logs ) + count( $error_logs );
                        $success_rate = $total > 0 ? round( ( count( $success_logs ) / $total ) * 100 ) : 0;
                        ?>
                        <p style="font-size: 24px; margin: 0;"><strong><?php echo $success_rate; ?>%</strong></p>
                        <small><?php echo count( $success_logs ); ?> success, <?php echo count( $error_logs ); ?> errors</small>
                    </div>
                </div>
            </div>

            <?php if ( !empty( $recent_posts ) ): ?>
            <div class="card" style="margin-top: 20px;">
                <h2>Recent Generated Posts</h2>
                <?php self::display_recent_posts( $recent_posts ); ?>
            </div>
            <?php endif; ?>
        </div>
        <?php
    }

    public static function security_page() {
        $security_logs = AIBPG_Security::get_security_logs();

        // Handle clear logs action
        if ( isset( $_GET['action'] ) && $_GET['action'] === 'clear_security_logs' ) {
            AIBPG_Security::validate_nonce( 'clear_security_logs' );
            AIBPG_Security::clear_security_logs();
            wp_redirect( admin_url( 'admin.php?page=aibpg-security&cleared=1' ) );
            exit;
        }

        ?>
        <div class="wrap">
            <h1>Security Logs</h1>

            <?php if ( isset( $_GET['cleared'] ) ): ?>
                <div class="notice notice-success is-dismissible">
                    <p>Security logs cleared successfully!</p>
                </div>
            <?php endif; ?>

            <div class="tablenav top">
                <div class="alignleft actions">
                    <a href="<?php echo wp_nonce_url( admin_url( 'admin.php?page=aibpg-security&action=clear_security_logs' ), 'clear_security_logs' ); ?>"
                       class="button" onclick="return confirm('Are you sure you want to clear all security logs?')">
                        Clear Security Logs
                    </a>
                </div>
            </div>

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col" style="width: 150px;">Timestamp</th>
                        <th scope="col" style="width: 120px;">Event</th>
                        <th scope="col" style="width: 80px;">User ID</th>
                        <th scope="col" style="width: 120px;">IP Address</th>
                        <th scope="col">Details</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( empty( $security_logs ) ): ?>
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px;">
                                <em>No security events logged</em>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ( array_reverse( $security_logs ) as $log ): ?>
                            <tr>
                                <td><?php echo esc_html( date( 'M j, Y H:i:s', strtotime( $log['timestamp'] ) ) ); ?></td>
                                <td>
                                    <span class="security-event security-<?php echo esc_attr( $log['event'] ); ?>">
                                        <?php echo esc_html( ucwords( str_replace( '_', ' ', $log['event'] ) ) ); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html( $log['user_id'] ?: 'N/A' ); ?></td>
                                <td><?php echo esc_html( $log['ip_address'] ); ?></td>
                                <td>
                                    <?php if ( ! empty( $log['details'] ) ): ?>
                                        <details>
                                            <summary>View Details</summary>
                                            <pre style="font-size: 11px; margin-top: 5px;"><?php echo esc_html( wp_json_encode( $log['details'], JSON_PRETTY_PRINT ) ); ?></pre>
                                        </details>
                                    <?php else: ?>
                                        <em>No additional details</em>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>

            <div class="card" style="margin-top: 20px;">
                <h3>Security Information</h3>
                <p><strong>Current Security Measures:</strong></p>
                <ul>
                    <li>✓ API keys are encrypted in the database</li>
                    <li>✓ Rate limiting on sensitive actions</li>
                    <li>✓ Input validation and sanitization</li>
                    <li>✓ Nonce verification for all forms</li>
                    <li>✓ User permission checks</li>
                    <li>✓ Security event logging</li>
                    <li>✓ Content sanitization for generated posts</li>
                </ul>
            </div>
        </div>

        <style>
        .security-event { padding: 3px 8px; border-radius: 3px; font-size: 12px; font-weight: bold; }
        .security-unauthorized_access, .security-invalid_nonce, .security-rate_limit_exceeded {
            background: #f8d7da; color: #721c24;
        }
        .security-settings_updated, .security-analytics_exported {
            background: #d4edda; color: #155724;
        }
        .security-invalid_settings {
            background: #fff3cd; color: #856404;
        }
        details summary { cursor: pointer; }
        </style>

        <script>
        jQuery(document).ready(function($) {
            $('#test-current-connection').click(function() {
                var btn = $(this);
                var resultDiv = $('#connection-test-result');

                btn.prop('disabled', true).text('Testing...');
                resultDiv.hide();

                $.post(ajaxurl, {
                    action: 'aibpg_test_current_connection',
                    nonce: '<?php echo wp_create_nonce( "aibpg_test_current_connection" ); ?>'
                }, function(response) {
                    btn.prop('disabled', false).text('Test Connection');

                    if (response.success) {
                        resultDiv.html('<div style="color: green; padding: 8px; background: #d4edda; border-radius: 4px;">✓ ' + response.data + '</div>');
                    } else {
                        resultDiv.html('<div style="color: red; padding: 8px; background: #f8d7da; border-radius: 4px;">✗ ' + response.data + '</div>');
                    }

                    resultDiv.show();
                }).fail(function() {
                    btn.prop('disabled', false).text('Test Connection');
                    resultDiv.html('<div style="color: red; padding: 8px; background: #f8d7da; border-radius: 4px;">✗ Connection test failed</div>').show();
                });
            });
        });
        </script>
        <?php
    }

    public static function handle_actions() {
        if ( !current_user_can( 'manage_options' ) ) {
            return;
        }

        $action = $_GET['action'] ?? '';

        if ( $action === 'clear_logs' && $_GET['page'] === 'aibpg-logs' ) {
            delete_option( 'aibpg_error_logs' );
            delete_option( 'aibpg_success_logs' );
            wp_redirect( admin_url( 'admin.php?page=aibpg-logs&cleared=1' ) );
            exit;
        }
    }

    public static function enqueue_scripts( $hook ) {
        if ( strpos( $hook, 'aibpg' ) === false ) {
            return;
        }

        wp_enqueue_script( 'jquery' );
    }

    public static function ajax_manual_generate() {
        check_ajax_referer( 'aibpg_manual_generate', 'nonce' );

        AIBPG_Security::validate_user_permissions();

        // Rate limiting
        $rate_limit = AIBPG_Security::rate_limit_check( 'manual_generate', 5, 300 ); // 5 per 5 minutes
        if ( is_wp_error( $rate_limit ) ) {
            AIBPG_Security::log_security_event( 'rate_limit_exceeded', [ 'action' => 'manual_generate' ] );
            wp_send_json_error( $rate_limit->get_error_message() );
        }

        $result = AIBPG_Scheduler::manual_generate();

        if ( $result ) {
            $post = get_post( $result );
            wp_send_json_success( "Post '{$post->post_title}' generated successfully! (ID: {$result})" );
        } else {
            wp_send_json_error( 'Failed to generate post. Check logs for details.' );
        }
    }

    public static function ajax_test_api() {
        check_ajax_referer( 'aibpg_test_api', 'nonce' );

        AIBPG_Security::validate_user_permissions();

        // Rate limiting for API testing
        $rate_limit = AIBPG_Security::rate_limit_check( 'test_api', 10, 600 ); // 10 per 10 minutes
        if ( is_wp_error( $rate_limit ) ) {
            AIBPG_Security::log_security_event( 'rate_limit_exceeded', [ 'action' => 'test_api' ] );
            wp_send_json_error( $rate_limit->get_error_message() );
        }

        $provider = sanitize_text_field( $_POST['provider'] );
        $api_key = sanitize_text_field( $_POST['api_key'] );

        // Validate API key format
        $validation = AIBPG_Security::validate_api_key( $api_key, $provider );
        if ( is_wp_error( $validation ) ) {
            wp_send_json_error( $validation->get_error_message() );
        }

        if ( $provider === 'openai' ) {
            $test_provider = new AIBPG_OpenAI_Provider();
            // Temporarily set the API key for testing
            $reflection = new ReflectionClass( $test_provider );
            $property = $reflection->getProperty( 'api_key' );
            $property->setAccessible( true );
            $property->setValue( $test_provider, $api_key );

            $result = $test_provider->generate_content( 'Test message' );

        } elseif ( $provider === 'gemini' ) {
            $test_provider = new AIBPG_Gemini_Provider();
            // Temporarily set the API key for testing
            $reflection = new ReflectionClass( $test_provider );
            $property = $reflection->getProperty( 'api_key' );
            $property->setAccessible( true );
            $property->setValue( $test_provider, $api_key );

            $result = $test_provider->generate_content( 'Test message' );

        } elseif ( $provider === 'openrouter' ) {
            // Temporarily update settings for testing
            $original_key = AIBPG_Settings::get( 'openrouter_api_key' );
            $original_model = AIBPG_Settings::get( 'openrouter_model' );

            AIBPG_Settings::update( 'openrouter_api_key', $api_key );

            // Also get the selected model from the form
            $selected_model = sanitize_text_field( $_POST['model'] ?? 'mistralai/mistral-7b-instruct:free' );
            AIBPG_Settings::update( 'openrouter_model', $selected_model );

            $test_provider = new AIBPG_OpenRouter_Provider();
            $test_result = $test_provider->test_connection();

            // Restore original settings
            AIBPG_Settings::update( 'openrouter_api_key', $original_key );
            AIBPG_Settings::update( 'openrouter_model', $original_model );

            if ( $test_result['success'] ) {
                $success_message = $test_result['message'] . ' Model: ' . $test_result['model'];
                if ( !empty( $test_result['response_preview'] ) ) {
                    $success_message .= ' Response: "' . $test_result['response_preview'] . '"';
                }
                wp_send_json_success( $success_message );
            } else {
                $error_message = $test_result['message'];

                // Add debug information if available
                if ( !empty( $test_result['debug'] ) ) {
                    $debug = $test_result['debug'];
                    if ( isset( $debug['response_code'] ) ) {
                        $error_message .= ' (HTTP ' . $debug['response_code'] . ')';
                    }
                    if ( isset( $debug['api_error']['code'] ) ) {
                        $error_message .= ' Error: ' . $debug['api_error']['code'];
                    }
                }

                wp_send_json_error( $error_message );
            }

        } else {
            wp_send_json_error( 'Invalid provider' );
        }

        if ( !empty( $result ) ) {
            wp_send_json_success( 'API connection successful!' );
        } else {
            wp_send_json_error( 'API connection failed. Check your API key.' );
        }
    }

    public static function ajax_test_current_connection() {
        check_ajax_referer( 'aibpg_test_current_connection', 'nonce' );

        AIBPG_Security::validate_user_permissions();

        $test_result = self::test_current_ai_configuration();

        if ( $test_result['success'] ) {
            wp_send_json_success( $test_result['message'] );
        } else {
            wp_send_json_error( $test_result['message'] );
        }
    }

    public static function ajax_export_analytics() {
        check_ajax_referer( 'aibpg_export', 'nonce' );

        AIBPG_Security::validate_user_permissions();

        // Rate limiting for exports
        $rate_limit = AIBPG_Security::rate_limit_check( 'export_analytics', 3, 3600 ); // 3 per hour
        if ( is_wp_error( $rate_limit ) ) {
            AIBPG_Security::log_security_event( 'rate_limit_exceeded', [ 'action' => 'export_analytics' ] );
            wp_die( $rate_limit->get_error_message() );
        }

        $format = sanitize_text_field( $_GET['format'] ?? 'csv' );
        if ( ! in_array( $format, [ 'csv', 'json' ] ) ) {
            wp_die( 'Invalid export format' );
        }

        AIBPG_Security::log_security_event( 'analytics_exported', [ 'format' => $format ] );

        $data = AIBPG_Analytics::export_analytics( $format );

        $filename = 'aibpg-analytics-' . date( 'Y-m-d' ) . '.' . $format;

        header( 'Content-Type: application/octet-stream' );
        header( 'Content-Disposition: attachment; filename="' . $filename . '"' );
        header( 'Content-Length: ' . strlen( $data ) );

        echo $data;
        exit;
    }

    private static function display_recent_posts( $posts = null ) {
        if ( $posts === null ) {
            $posts = get_posts([
                'meta_key' => '_aibpg_generated',
                'meta_value' => true,
                'numberposts' => 10,
                'post_status' => 'any'
            ]);
        }

        if ( empty( $posts ) ) {
            echo '<p><em>No generated posts found.</em></p>';
            return;
        }

        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>Title</th><th>Status</th><th>Topic</th><th>Generated</th><th>Actions</th></tr></thead>';
        echo '<tbody>';

        foreach ( $posts as $post ) {
            $topic = get_post_meta( $post->ID, '_aibpg_topic', true );
            $generated_date = get_post_meta( $post->ID, '_aibpg_generated_date', true );

            echo '<tr>';
            echo '<td><strong>' . esc_html( $post->post_title ) . '</strong></td>';
            echo '<td><span class="post-status-' . $post->post_status . '">' . ucfirst( $post->post_status ) . '</span></td>';
            echo '<td>' . esc_html( $topic ?: 'N/A' ) . '</td>';
            echo '<td>' . ( $generated_date ? date( 'M j, Y H:i', strtotime( $generated_date ) ) : 'N/A' ) . '</td>';
            echo '<td>';
            echo '<a href="' . get_edit_post_link( $post->ID ) . '" class="button button-small">Edit</a> ';
            echo '<a href="' . get_permalink( $post->ID ) . '" class="button button-small" target="_blank">View</a>';
            echo '</td>';
            echo '</tr>';
        }

        echo '</tbody></table>';
    }

    private static function check_api_status( $settings ) {
        $status = [
            'ai_configured' => false,
            'image_configured' => false
        ];

        // Check AI provider
        if ( $settings['ai_provider'] === 'openai' && !empty( $settings['openai_api_key'] ) ) {
            $status['ai_configured'] = true;
        } elseif ( $settings['ai_provider'] === 'gemini' && !empty( $settings['gemini_api_key'] ) ) {
            $status['ai_configured'] = true;
        } elseif ( $settings['ai_provider'] === 'openrouter' && !empty( $settings['openrouter_api_key'] ) ) {
            $status['ai_configured'] = true;
        }

        // Check image provider
        if ( $settings['image_provider'] === 'pexels' && !empty( $settings['pexels_api_key'] ) ) {
            $status['image_configured'] = true;
        } elseif ( $settings['image_provider'] === 'none' ) {
            $status['image_configured'] = true;
        }

        return $status;
    }

    private static function test_current_ai_configuration() {
        $settings = AIBPG_Settings::all();
        $api_status = self::check_api_status( $settings );

        if ( !$api_status['ai_configured'] ) {
            return [
                'success' => false,
                'message' => 'AI provider not configured'
            ];
        }

        try {
            switch ( $settings['ai_provider'] ) {
                case 'openai':
                    if ( class_exists( 'AIBPG_OpenAI_Provider' ) ) {
                        $provider = new AIBPG_OpenAI_Provider();
                        return $provider->test_connection();
                    }
                    break;

                case 'gemini':
                    if ( class_exists( 'AIBPG_Gemini_Provider' ) ) {
                        $provider = new AIBPG_Gemini_Provider();
                        return $provider->test_connection();
                    }
                    break;

                case 'openrouter':
                    if ( class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
                        $provider = new AIBPG_OpenRouter_Provider();
                        return $provider->test_connection();
                    }
                    break;
            }
        } catch ( Exception $e ) {
            return [
                'success' => false,
                'message' => 'Error testing connection: ' . $e->getMessage()
            ];
        }

        return [
            'success' => false,
            'message' => 'Provider class not available'
        ];
    }
}