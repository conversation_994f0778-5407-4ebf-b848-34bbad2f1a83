<?php
/**
 * Quick test to verify blog post generation is working
 * Access this via: /wp-admin/admin.php?page=quick-generation-test.php
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>🚀 Quick Generation Test</h2>";

// Step 1: Check basic requirements
echo "<h3>Step 1: Basic Requirements</h3>";
$requirements = [
    'AIBPG_Settings' => class_exists( 'AIBPG_Settings' ),
    'AIBPG_Generator' => class_exists( 'AIBPG_Generator' ),
    'AIBPG_Topic_Manager' => class_exists( 'AIBPG_Topic_Manager' ),
    'AIBPG_Content_Templates' => class_exists( 'AIBPG_Content_Templates' ),
    'AIBPG_Content_Processor' => class_exists( 'AIBPG_Content_Processor' ),
    'AIBPG_OpenRouter_Provider' => class_exists( 'AIBPG_OpenRouter_Provider' )
];

$all_good = true;
foreach ( $requirements as $class => $exists ) {
    $status = $exists ? '✅' : '❌';
    echo "<p>{$status} <strong>{$class}</strong></p>";
    if ( !$exists ) $all_good = false;
}

if ( !$all_good ) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Missing Required Classes</h4>";
    echo "<p>Cannot proceed with generation test. Please ensure all plugin files are properly loaded.</p>";
    echo "</div>";
    exit;
}

// Step 2: Check settings
echo "<h3>Step 2: Settings Check</h3>";
$settings = AIBPG_Settings::all();

$ai_provider = $settings['ai_provider'] ?? 'Not set';
$api_key_field = $ai_provider . '_api_key';
$api_key = $settings[$api_key_field] ?? '';
$categories = $settings['categories'] ?? [];

echo "<p><strong>AI Provider:</strong> {$ai_provider} " . ( empty( $ai_provider ) || $ai_provider === 'Not set' ? '❌' : '✅' ) . "</p>";
echo "<p><strong>API Key:</strong> " . ( empty( $api_key ) ? 'Not set ❌' : 'Set (' . strlen( $api_key ) . ' chars) ✅' ) . "</p>";
echo "<p><strong>Categories:</strong> " . ( empty( $categories ) ? 'None ⚠️' : implode( ', ', $categories ) . ' ✅' ) . "</p>";

if ( empty( $ai_provider ) || $ai_provider === 'Not set' || empty( $api_key ) ) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Configuration Issue</h4>";
    echo "<p>AI provider or API key not configured. Please configure in Settings.</p>";
    echo "</div>";
    exit;
}

// Step 3: Test topic availability
echo "<h3>Step 3: Topic Availability</h3>";
$available_categories = AIBPG_Topic_Manager::get_available_categories();
$test_category = !empty( $categories ) ? $categories[0] : 'tech';

echo "<p><strong>Available Categories:</strong> " . implode( ', ', $available_categories ) . "</p>";
echo "<p><strong>Test Category:</strong> {$test_category}</p>";

$test_topic = AIBPG_Topic_Manager::get_next_topic( $test_category );
if ( $test_topic ) {
    echo "<p><strong>Test Topic:</strong> {$test_topic} ✅</p>";
} else {
    echo "<p><strong>Test Topic:</strong> None available ❌</p>";
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ No Topics Available</h4>";
    echo "<p>No topics available for category '{$test_category}'. This will prevent generation.</p>";
    echo "</div>";
    exit;
}

// Step 4: Test API connection
echo "<h3>Step 4: API Connection Test</h3>";
try {
    if ( $ai_provider === 'openrouter' ) {
        $provider = new AIBPG_OpenRouter_Provider();
        $connection_test = $provider->test_connection();
        
        if ( $connection_test['success'] ) {
            echo "<p>✅ <strong>API Connection:</strong> {$connection_test['message']}</p>";
            echo "<p><strong>Model:</strong> {$connection_test['model']}</p>";
        } else {
            echo "<p>❌ <strong>API Connection Failed:</strong> {$connection_test['message']}</p>";
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ API Connection Issue</h4>";
            echo "<p>Cannot connect to API. Check your API key and model configuration.</p>";
            echo "</div>";
            exit;
        }
    }
} catch ( Exception $e ) {
    echo "<p>❌ <strong>API Test Error:</strong> " . $e->getMessage() . "</p>";
    exit;
}

// Step 5: Test content generation
echo "<h3>Step 5: Content Generation Test</h3>";
try {
    $prompt = AIBPG_Content_Templates::get_content_prompt( $test_topic, $test_category, 'en' );
    echo "<p>✅ <strong>Content Prompt Generated:</strong> " . strlen( $prompt ) . " characters</p>";
    
    $raw_content = $provider->generate_content( $prompt );
    if ( $raw_content ) {
        echo "<p>✅ <strong>Raw Content Generated:</strong> " . strlen( $raw_content ) . " characters</p>";
        
        $processed_content = AIBPG_Content_Processor::process_content( $raw_content );
        if ( $processed_content ) {
            echo "<p>✅ <strong>Content Processed:</strong> " . strlen( $processed_content ) . " characters</p>";
        } else {
            echo "<p>❌ <strong>Content Processing Failed</strong></p>";
            exit;
        }
    } else {
        echo "<p>❌ <strong>Content Generation Failed</strong></p>";
        exit;
    }
} catch ( Exception $e ) {
    echo "<p>❌ <strong>Content Generation Error:</strong> " . $e->getMessage() . "</p>";
    exit;
}

// Step 6: Test meta generation
echo "<h3>Step 6: Meta Generation Test</h3>";
try {
    $meta_prompt = AIBPG_Content_Templates::get_meta_prompt( $test_topic, $test_category, 'en' );
    $meta = $provider->generate_meta( $meta_prompt );
    
    if ( $meta && !empty( $meta['title'] ) ) {
        echo "<p>✅ <strong>Meta Generated:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Title:</strong> {$meta['title']}</li>";
        echo "<li><strong>Description:</strong> {$meta['description']}</li>";
        echo "</ul>";
    } else {
        echo "<p>❌ <strong>Meta Generation Failed</strong></p>";
        exit;
    }
} catch ( Exception $e ) {
    echo "<p>❌ <strong>Meta Generation Error:</strong> " . $e->getMessage() . "</p>";
    exit;
}

// Step 7: Full generation test
echo "<h3>Step 7: Full Generation Test</h3>";
echo "<button type='button' id='run-full-test' class='button button-primary button-large'>Run Full Generation Test</button>";
echo "<div id='full-test-result' style='margin-top: 20px;'></div>";

echo "<script>
jQuery(document).ready(function($) {
    $('#run-full-test').click(function() {
        var btn = $(this);
        var resultDiv = $('#full-test-result');
        
        btn.prop('disabled', true).text('Running Full Test...');
        resultDiv.html('<div style=\"background: #e7f3ff; padding: 15px; border-radius: 5px;\"><p>🔄 Running full generation test...</p></div>');
        
        $.post(ajaxurl, {
            action: 'aibpg_manual_generate',
            nonce: '" . wp_create_nonce( 'aibpg_manual_generate' ) . "'
        }, function(response) {
            btn.prop('disabled', false).text('Run Full Generation Test');
            
            if (response.success) {
                resultDiv.html('<div style=\"background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;\"><h4>🎉 SUCCESS! Blog Post Generated</h4><p>' + response.data + '</p><p><strong>The plugin is now working correctly!</strong></p></div>');
            } else {
                resultDiv.html('<div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;\"><h4>❌ Generation Failed</h4><p>' + response.data + '</p></div>');
            }
        }).fail(function(xhr, status, error) {
            btn.prop('disabled', false).text('Run Full Generation Test');
            resultDiv.html('<div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;\"><h4>❌ AJAX Error</h4><p>Status: ' + status + '<br>Error: ' + error + '</p></div>');
        });
    });
});
</script>";

// Step 8: New models test
echo "<h3>Step 8: New AI Models</h3>";
$available_models = AIBPG_OpenRouter_Provider::get_available_models();
$new_models = [
    'qwen/qwen-2.5-coder-32b-instruct' => 'Qwen3 Coder',
    'google/gemma-2-2b-it:free' => 'Gemma 3n E2B IT'
];

echo "<p><strong>New Models Added:</strong></p>";
echo "<ul>";
foreach ( $new_models as $model_id => $model_name ) {
    $exists = isset( $available_models[$model_id] );
    $status = $exists ? '✅' : '❌';
    echo "<li>{$status} <strong>{$model_name}</strong> ({$model_id})</li>";
    if ( $exists ) {
        echo "<ul><li><em>{$available_models[$model_id]['description']}</em></li></ul>";
    }
}
echo "</ul>";

echo "<h3>🎯 Summary</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>Pre-Generation Checks Complete</h4>";
echo "<p>All preliminary checks passed! The plugin should be ready to generate blog posts.</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Click the 'Run Full Generation Test' button above</li>";
echo "<li>If successful, go to AI Blog Post Generator → Generate Post</li>";
echo "<li>Try the new AI models in Settings</li>";
echo "<li>Check that scheduled posts are working</li>";
echo "</ol>";
echo "</div>";
?>
