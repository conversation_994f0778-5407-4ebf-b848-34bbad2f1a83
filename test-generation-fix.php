<?php
/**
 * Test the blog post generation fixes and new AI models
 */

// Only run if accessed by admin
if ( !current_user_can( 'manage_options' ) ) {
    wp_die( 'Unauthorized' );
}

echo "<h2>🔧 Testing Blog Post Generation Fixes</h2>";

// Test 1: Check if all required methods exist
echo "<h3>1. Method Availability Check</h3>";
$required_methods = [
    'AIBPG_Generator' => ['generate', 'validate_meta', 'get_ai_provider', 'normalize_category', 'log_error'],
    'AIBPG_Content_Templates' => ['get_content_prompt', 'get_meta_prompt'],
    'AIBPG_Content_Processor' => ['process_content'],
    'AIBPG_OpenRouter_Provider' => ['generate_content', 'generate_meta', 'test_connection']
];

$all_methods_exist = true;
foreach ( $required_methods as $class => $methods ) {
    echo "<h4>{$class}</h4>";
    if ( class_exists( $class ) ) {
        echo "<ul>";
        foreach ( $methods as $method ) {
            $exists = method_exists( $class, $method );
            $status = $exists ? '✅' : '❌';
            echo "<li>{$status} <strong>{$method}()</strong></li>";
            if ( !$exists ) $all_methods_exist = false;
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Class not found</p>";
        $all_methods_exist = false;
    }
}

if ( $all_methods_exist ) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ All Required Methods Available</h4>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Some Methods Missing</h4>";
    echo "<p>Generation will fail until missing methods are implemented.</p>";
    echo "</div>";
}

// Test 2: Check new AI models
echo "<h3>2. New AI Models Check</h3>";
if ( class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    $available_models = AIBPG_OpenRouter_Provider::get_available_models();
    
    $new_models = [
        'qwen/qwen-2.5-coder-32b-instruct' => 'Qwen3 Coder',
        'google/gemma-2-2b-it:free' => 'Gemma 3n E2B IT'
    ];
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr><th>Model ID</th><th>Name</th><th>Status</th><th>Description</th></tr>";
    
    foreach ( $new_models as $model_id => $expected_name ) {
        if ( isset( $available_models[$model_id] ) ) {
            $model = $available_models[$model_id];
            echo "<tr>";
            echo "<td><strong>{$model_id}</strong></td>";
            echo "<td>{$model['name']}</td>";
            echo "<td>✅ Available</td>";
            echo "<td>" . substr( $model['description'], 0, 100 ) . "...</td>";
            echo "</tr>";
        } else {
            echo "<tr>";
            echo "<td><strong>{$model_id}</strong></td>";
            echo "<td>{$expected_name}</td>";
            echo "<td>❌ Missing</td>";
            echo "<td>Not found in available models</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    echo "<p><strong>Total Models Available:</strong> " . count( $available_models ) . "</p>";
    
} else {
    echo "<p style='color: red;'>❌ AIBPG_OpenRouter_Provider class not available</p>";
}

// Test 3: Test generation with different models
echo "<h3>3. Model Generation Test</h3>";
if ( class_exists( 'AIBPG_Settings' ) && class_exists( 'AIBPG_OpenRouter_Provider' ) ) {
    $settings = AIBPG_Settings::all();
    
    if ( !empty( $settings['openrouter_api_key'] ) ) {
        $test_models = [
            'mistralai/mistral-7b-instruct:free' => 'Mistral 7B (Original)',
            'qwen/qwen-2.5-coder-32b-instruct' => 'Qwen3 Coder (New)',
            'google/gemma-2-2b-it:free' => 'Gemma 3n E2B IT (New)'
        ];
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr><th>Model</th><th>Connection Test</th><th>Content Generation</th><th>Response Quality</th></tr>";
        
        foreach ( $test_models as $model_id => $model_name ) {
            echo "<tr>";
            echo "<td><strong>{$model_name}</strong><br><small>{$model_id}</small></td>";
            
            // Test connection
            $original_model = $settings['openrouter_model'];
            AIBPG_Settings::update( 'openrouter_model', $model_id );
            
            try {
                $provider = new AIBPG_OpenRouter_Provider();
                $connection_test = $provider->test_connection();
                
                $connection_status = $connection_test['success'] ? '✅ Connected' : '❌ Failed';
                echo "<td>{$connection_status}</td>";
                
                if ( $connection_test['success'] ) {
                    // Test content generation
                    $test_prompt = "Write a brief introduction about artificial intelligence in 50 words.";
                    $content = $provider->generate_content( $test_prompt );
                    
                    if ( !empty( $content ) ) {
                        $word_count = str_word_count( $content );
                        echo "<td>✅ Generated ({$word_count} words)</td>";
                        
                        // Quality check
                        $quality_score = 0;
                        if ( $word_count >= 30 ) $quality_score++;
                        if ( strpos( strtolower( $content ), 'artificial intelligence' ) !== false ) $quality_score++;
                        if ( strlen( $content ) > 100 ) $quality_score++;
                        
                        $quality_text = $quality_score >= 2 ? '✅ Good' : '⚠️ Basic';
                        echo "<td>{$quality_text}<br><small>" . substr( $content, 0, 100 ) . "...</small></td>";
                    } else {
                        echo "<td>❌ No content</td>";
                        echo "<td>❌ Failed</td>";
                    }
                } else {
                    echo "<td>❌ Cannot test</td>";
                    echo "<td>❌ Cannot test</td>";
                }
                
            } catch ( Exception $e ) {
                echo "<td>❌ Error</td>";
                echo "<td>❌ Error</td>";
                echo "<td>Error: " . $e->getMessage() . "</td>";
            }
            
            echo "</tr>";
        }
        
        // Restore original model
        AIBPG_Settings::update( 'openrouter_model', $original_model );
        
        echo "</table>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ OpenRouter API key not configured - cannot test models</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Required classes not available</p>";
}

// Test 4: Full generation test
echo "<h3>4. Full Blog Post Generation Test</h3>";
if ( $all_methods_exist && class_exists( 'AIBPG_Generator' ) ) {
    echo "<p>Attempting full blog post generation...</p>";
    
    try {
        $start_time = microtime( true );
        $post_id = AIBPG_Generator::generate();
        $end_time = microtime( true );
        
        $generation_time = round( $end_time - $start_time, 2 );
        
        if ( $post_id ) {
            $post = get_post( $post_id );
            
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Blog Post Generated Successfully!</h4>";
            echo "<p><strong>Post ID:</strong> {$post_id}</p>";
            echo "<p><strong>Title:</strong> {$post->post_title}</p>";
            echo "<p><strong>Status:</strong> {$post->post_status}</p>";
            echo "<p><strong>Content Length:</strong> " . strlen( $post->post_content ) . " characters</p>";
            echo "<p><strong>Word Count:</strong> " . str_word_count( $post->post_content ) . " words</p>";
            echo "<p><strong>Generation Time:</strong> {$generation_time} seconds</p>";
            echo "<p><strong>View Post:</strong> <a href='" . get_permalink( $post_id ) . "' target='_blank'>View</a> | <a href='" . get_edit_post_link( $post_id ) . "' target='_blank'>Edit</a></p>";
            echo "</div>";
            
            // Content quality analysis
            echo "<h4>Content Quality Analysis:</h4>";
            $content = $post->post_content;
            
            echo "<ul>";
            echo "<li><strong>Has Headings:</strong> " . ( preg_match( '/<h[1-6]/', $content ) ? '✅ Yes' : '❌ No' ) . "</li>";
            echo "<li><strong>Has Bold Text:</strong> " . ( preg_match( '/<strong>/', $content ) ? '✅ Yes' : '❌ No' ) . "</li>";
            echo "<li><strong>Has Lists:</strong> " . ( preg_match( '/<[uo]l>/', $content ) ? '✅ Yes' : '❌ No' ) . "</li>";
            echo "<li><strong>Has Paragraphs:</strong> " . ( preg_match( '/<p>/', $content ) ? '✅ Yes' : '❌ No' ) . "</li>";
            echo "<li><strong>No Raw Markdown:</strong> " . ( !preg_match( '/#{1,6}\s|\*\*.*?\*\*/', $content ) ? '✅ Clean' : '❌ Contains markdown' ) . "</li>";
            echo "</ul>";
            
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Blog Post Generation Failed</h4>";
            echo "<p>No post was created. Check the error logs for details.</p>";
            echo "<p><strong>Generation Time:</strong> {$generation_time} seconds</p>";
            echo "</div>";
        }
        
    } catch ( Exception $e ) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Generation Error</h4>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>File:</strong> " . $e->getFile() . ":" . $e->getLine() . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Cannot test generation - missing required methods</p>";
}

// Test 5: Manual generation button test
echo "<h3>5. Manual Generation Interface Test</h3>";
echo "<p>Test the manual generation button:</p>";
echo "<button type='button' id='test-manual-generate' class='button button-primary'>Test Manual Generation</button>";
echo "<div id='manual-test-result' style='margin-top: 15px;'></div>";

echo "<script>
jQuery(document).ready(function($) {
    $('#test-manual-generate').click(function() {
        var btn = $(this);
        var resultDiv = $('#manual-test-result');
        
        btn.prop('disabled', true).text('Testing...');
        resultDiv.html('<p>Testing manual generation...</p>');
        
        $.post(ajaxurl, {
            action: 'aibpg_manual_generate',
            nonce: '" . wp_create_nonce( 'aibpg_manual_generate' ) . "'
        }, function(response) {
            btn.prop('disabled', false).text('Test Manual Generation');
            
            if (response.success) {
                resultDiv.html('<div style=\"background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;\"><h4>✅ Manual Generation Successful</h4><p>' + response.data + '</p></div>');
            } else {
                resultDiv.html('<div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;\"><h4>❌ Manual Generation Failed</h4><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            btn.prop('disabled', false).text('Test Manual Generation');
            resultDiv.html('<div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;\"><h4>❌ AJAX Error</h4><p>Failed to communicate with server</p></div>');
        });
    });
});
</script>";

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>Required Methods:</strong> " . ( $all_methods_exist ? '✅ All present' : '❌ Some missing' ) . "</li>";
echo "<li><strong>New AI Models:</strong> " . ( class_exists( 'AIBPG_OpenRouter_Provider' ) ? '✅ Added' : '❌ Not available' ) . "</li>";
echo "<li><strong>Generation Function:</strong> " . ( isset( $post_id ) && $post_id ? '✅ Working' : '⚠️ Needs testing' ) . "</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>If all tests show ✅, the plugin should now be working</li>";
echo "<li>Try the manual generation button above</li>";
echo "<li>Go to AI Blog Post Generator → Generate Post to test the interface</li>";
echo "<li>Check that scheduled posts are being created</li>";
echo "<li>Test the new AI models (Qwen3 Coder and Gemma 3n E2B IT)</li>";
echo "</ol>";
?>
