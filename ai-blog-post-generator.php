<?php
/*
Plugin Name: AI Blog Post Generator
Plugin URI: https://github.com/yourusername/ai-blog-post-generator
Description: Advanced AI-powered blog post generator with unique topic management, SEO optimization, image integration, analytics, and comprehensive automation features. Supports OpenAI and Google Gemini.
Version: 1.0.0
Author: Your Name
Author URI: https://yourwebsite.com
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: ai-blog-post-generator
Domain Path: /languages
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Network: false

AI Blog Post Generator is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 2 of the License, or
any later version.

AI Blog Post Generator is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with AI Blog Post Generator. If not, see https://www.gnu.org/licenses/gpl-2.0.html.
*/

if ( ! defined( 'ABSPATH' ) ) exit;

define( 'AIBPG_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'AIBPG_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'AIBPG_VERSION', '1.0.0' );

require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-autoloader.php';
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-main.php';

add_action( 'plugins_loaded', [ 'AIBPG_Main', 'init' ] );

// Activation/Deactivation hooks with proper class loading
register_activation_hook( __FILE__, 'aibpg_activate_plugin' );
register_deactivation_hook( __FILE__, 'aibpg_deactivate_plugin' );

function aibpg_activate_plugin() {
    // Ensure all classes are loaded before activation
    require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-scheduler.php';
    require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-settings.php';

    // Now safe to call activation
    AIBPG_Scheduler::activate();
}

function aibpg_deactivate_plugin() {
    // Ensure scheduler class is loaded
    require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-scheduler.php';

    // Now safe to call deactivation
    AIBPG_Scheduler::deactivate();
}